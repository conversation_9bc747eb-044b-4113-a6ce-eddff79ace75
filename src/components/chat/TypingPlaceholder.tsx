import React, { useState, useEffect } from 'react';

interface TypingPlaceholderProps {
  examples: string[];
  className?: string;
  shouldStop?: boolean;
}

const TypingPlaceholder: React.FC<TypingPlaceholderProps> = ({ examples, className = "", shouldStop = false }) => {
  const [currentIndex, setCurrentIndex] = useState(0);
  const [displayText, setDisplayText] = useState('');
  const [isTyping, setIsTyping] = useState(true);
  const [isDeleting, setIsDeleting] = useState(false);
  const [isActive, setIsActive] = useState(true);

  useEffect(() => {
    if (examples.length === 0 || !isActive || shouldStop) return;

    const currentExample = examples[currentIndex];

    const typeSpeed = 100; // Typing speed in ms
    const deleteSpeed = 50; // Deleting speed in ms
    const pauseTime = 2000; // Pause time at end of word in ms
    const fadeTime = 300; // Fade transition time in ms

    let timeout: NodeJS.Timeout;

    if (isTyping && !isDeleting) {
      // Typing forward
      if (displayText.length < currentExample.length) {
        timeout = setTimeout(() => {
          setDisplayText(currentExample.slice(0, displayText.length + 1));
        }, typeSpeed);
      } else {
        // Finished typing, pause then start deleting
        timeout = setTimeout(() => {
          setIsDeleting(true);
          setIsTyping(false);
        }, pauseTime);
      }
    } else if (isDeleting && !isTyping) {
      // Deleting backward
      if (displayText.length > 0) {
        timeout = setTimeout(() => {
          setDisplayText(displayText.slice(0, -1));
        }, deleteSpeed);
      } else {
        // Finished deleting, move to next example and restart typing
        const nextIndex = (currentIndex + 1) % examples.length;
        setCurrentIndex(nextIndex);
        setIsDeleting(false);

        // Small pause before starting to type next word
        timeout = setTimeout(() => {
          setIsTyping(true);
        }, fadeTime);
      }
    }

    return () => clearTimeout(timeout);
  }, [displayText, isTyping, isDeleting, currentIndex, examples, isActive, shouldStop]);

  // Reset animation when examples change
  useEffect(() => {
    setCurrentIndex(0);
    setDisplayText('');
    setIsTyping(true);
    setIsDeleting(false);
    setIsActive(true);
  }, [examples]);

  // Stop animation when shouldStop prop changes
  useEffect(() => {
    if (shouldStop) {
      setIsActive(false);
      setDisplayText('');
    } else {
      setIsActive(true);
    }
  }, [shouldStop]);

  return (
    <span className={`${className} transition-opacity duration-300 ${!isActive ? 'opacity-0' : 'opacity-100'}`}>
      {isActive && displayText}
      {isActive && <span className="animate-pulse">|</span>}
    </span>
  );
};

export default TypingPlaceholder;
