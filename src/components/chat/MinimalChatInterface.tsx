import * as React from "react";
import { useState, useEffect, useRef } from "react";
import { useQuery, useQueryClient } from "@tanstack/react-query";
import { useToast } from "@/components/ui/use-toast";
import { supabase } from "@/integrations/supabase/client";
import { useNavigate, useParams } from "react-router-dom";
import { WelcomeHeading } from "@/components/ui/WelcomeHeading";
import MessageCounter from './MessageCounter';
import MinimalLoadingMessage from './MinimalLoadingMessage';
import TypingPlaceholder from './TypingPlaceholder';
import { useUserLimits } from '@/hooks/useUserLimits';
import { useAuth } from '@/contexts/AuthContext';
import { Plus, Trash2 } from 'lucide-react';

// =============================================
// TYPE DEFINITIONS
// =============================================
interface Message {
  role: string;
  content: {
    text: string | null;
    marketData?: any;
    webResults?: any[];
    symbol?: string | null;
    searchQueries?: string[];
    evaAnalysis?: any;
    symbols?: string[];
    isLoading?: boolean;
    analysisProgress?: any;
    aiAnalysis?: string;
    symbolTypes?: any;
    loadingPlaceholder?: boolean;
    isGeneralMessage?: boolean;
  };
}

interface ChatAIResponse {
  symbols: string[];
  symbolTypes: { [key: string]: 'STOCK' | 'CRYPTO' | 'MARKET' };
  marketData: any;
  isGeneralMessage: boolean;
}

interface ChatSession {
  id: string;
  title: string;
  created_at: string;
  updated_at: string;
  user_id: string;
  chat_type: string | null;
}

// =============================================
// MINIMAL CHAT INTERFACE COMPONENT
// =============================================
const MinimalChatInterface: React.FC = () => {
  // =============================================
  // STATE VARIABLES
  // =============================================
  const [message, setMessage] = useState("");
  const [messages, setMessages] = useState<Message[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [isTextareaFocused, setIsTextareaFocused] = useState(false);
  const [currentPlaceholderText, setCurrentPlaceholderText] = useState("");
  const [isAutoTyping, setIsAutoTyping] = useState(false);

  // Chat history state
  const [chatHistory, setChatHistory] = useState<ChatSession[]>([]);
  const [isLoadingHistory, setIsLoadingHistory] = useState(false);
  const [showHistory, setShowHistory] = useState(true);

  // Placeholder examples for typing animation
  const placeholderExamples = [
    "Analyze PLTR and TSLA",
    "Analyze GOOGL and MSFT",
    "What does Warren Buffett think of AMZN?",
    "Compare NVDA vs AMD performance",
    "What's driving Bitcoin's recent moves?",
    "Analyze the tech sector outlook",
    "Should I buy AAPL at current levels?",
    "What are the risks with Tesla stock?"
  ];

  // Get authentication state from useAuth hook
  const { isAuthenticated } = useAuth();

  // Add useUserLimits hook
  const {
    messagesRemaining,
    messagesLimit,
    incrementMessagesUsed,
    isLoading: isLoadingLimits,
    planType,
    hasReachedLimit
  } = useUserLimits();

  // =============================================
  // REFS, HOOKS, AND NAVIGATION
  // =============================================
  const textareaRef = useRef<HTMLTextAreaElement>(null);
  const { toast } = useToast();
  const navigate = useNavigate();
  const { id: currentChatId } = useParams();

  // Reset state for new chats
  useEffect(() => {
    if (!currentChatId) {
      setMessages([]);
      setIsLoading(false);
    }
  }, [currentChatId]);

  // Load chat history
  useEffect(() => {
    if (!isAuthenticated) return;

    const loadChatHistory = async () => {
      setIsLoadingHistory(true);
      try {
        const { data: { user } } = await supabase.auth.getUser();
        if (!user) return;

        const { data: chats, error } = await supabase
          .from('chats')
          .select('*')
          .eq('user_id', user.id)
          .order('updated_at', { ascending: false })
          .limit(20);

        if (error) throw error;
        setChatHistory(chats || []);
      } catch (error) {
        console.error('Error loading chat history:', error);
      } finally {
        setIsLoadingHistory(false);
      }
    };

    loadChatHistory();
  }, [isAuthenticated]);

  // Load existing chat messages
  useEffect(() => {
    if (!currentChatId || !isAuthenticated) {
      setMessages([]);
      return;
    }

    const loadChat = async () => {
      try {
        const { data: chatMessages, error } = await supabase
          .from('messages')
          .select('*')
          .eq('chat_id', currentChatId)
          .order('created_at', { ascending: true });

        if (error) throw error;

        if (chatMessages) {
          const formattedMessages = chatMessages.map(msg => ({
            role: msg.role,
            content: typeof msg.content === 'string' ? JSON.parse(msg.content) : msg.content
          }));
          setMessages(formattedMessages);
        }
      } catch (error) {
        toast({
          title: "Error",
          description: "Failed to load chat messages. Please try refreshing the page.",
          variant: "destructive"
        });
      }
    };

    loadChat();
  }, [currentChatId, isAuthenticated]);

  // =============================================
  // CHAT MANAGEMENT FUNCTIONS
  // =============================================
  const createNewChat = async () => {
    try {
      const { data: { user } } = await supabase.auth.getUser();
      if (!user) return;

      const { data: newChat, error } = await supabase
        .from('chats')
        .insert({
          user_id: user.id,
          title: 'New Chat',
          chat_type: 'ai'
        })
        .select()
        .single();

      if (error) throw error;

      // Update chat history
      setChatHistory(prev => [newChat, ...prev]);

      // Navigate to new chat
      navigate(`/chat/${newChat.id}`);
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to create new chat",
        variant: "destructive"
      });
    }
  };

  const deleteChat = async (chatId: string) => {
    try {
      const { error } = await supabase
        .from('chats')
        .delete()
        .eq('id', chatId);

      if (error) throw error;

      // Update chat history
      setChatHistory(prev => prev.filter(chat => chat.id !== chatId));

      // If we're currently viewing this chat, navigate to home
      if (currentChatId === chatId) {
        navigate('/chat');
      }

      toast({
        title: "Chat deleted",
        description: "Chat has been successfully deleted",
      });
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to delete chat",
        variant: "destructive"
      });
    }
  };

  const formatChatTime = (dateString: string) => {
    const date = new Date(dateString);
    const now = new Date();
    const diffInHours = (now.getTime() - date.getTime()) / (1000 * 60 * 60);

    if (diffInHours < 24) {
      return date.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });
    } else if (diffInHours < 168) { // 7 days
      return date.toLocaleDateString([], { weekday: 'short' });
    } else {
      return date.toLocaleDateString([], { month: 'short', day: 'numeric' });
    }
  };

  // =============================================
  // AUTO-TYPING FUNCTION
  // =============================================
  const autoTypeText = async (text: string) => {
    setIsAutoTyping(true);
    setMessage("");
    setIsTextareaFocused(true);

    for (let i = 0; i <= text.length; i++) {
      await new Promise(resolve => setTimeout(resolve, 50));
      setMessage(text.slice(0, i));
    }

    setIsAutoTyping(false);
    textareaRef.current?.focus();
  };

  // =============================================
  // MESSAGE SUBMISSION HANDLER
  // =============================================
  const queryClient = useQueryClient();

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    // Prevent free users from sending messages
    if (planType === 'free') {
      navigate('/subscription/manage');
      return;
    }

    if (!message.trim() || isLoading) return;

    // Check message limits
    const limitReached = await hasReachedLimit();
    if (limitReached) {
      toast({
        title: "Message limit reached",
        description: `You've reached your ${messagesLimit} message limit for your ${planType} plan. Please upgrade for more messages.`,
        variant: "destructive"
      });
      return;
    }

    try {
      setIsLoading(true);

      // Add user message immediately
      const userMessage: Message = {
        role: 'user',
        content: { text: message }
      };

      // Add loading message
      const loadingMessage: Message = {
        role: 'assistant',
        content: {
          text: null,
          loadingPlaceholder: true,
          isLoading: true
        }
      };

      setMessages(prev => [...prev, userMessage, loadingMessage]);
      setMessage("");

      // Auto-resize textarea
      if (textareaRef.current) {
        textareaRef.current.style.height = 'auto';
      }

      // Make API call
      const { data: response, error: marketError } = await supabase.functions.invoke<ChatAIResponse>('chat-ai', {
        body: {
          messages: [{ role: 'user', content: message }]
        }
      });

      if (marketError) {
        throw new Error(`API Error: ${marketError.message}`);
      }

      if (!response) {
        throw new Error('No response received from chat-ai function');
      }

      // Update the loading message with the response
      setMessages(prev => {
        const updated = [...prev];
        const lastIndex = updated.length - 1;
        if (updated[lastIndex] && updated[lastIndex].content.loadingPlaceholder) {
          updated[lastIndex] = {
            role: 'assistant',
            content: {
              text: response.isGeneralMessage ? response.marketData : null,
              marketData: response.marketData,
              symbols: response.symbols,
              symbolTypes: response.symbolTypes,
              isGeneralMessage: response.isGeneralMessage,
              isLoading: false,
              loadingPlaceholder: false
            }
          };
        }
        return updated;
      });

      // Increment message count
      await incrementMessagesUsed();

    } catch (error) {
      toast({
        title: 'Error',
        description: error instanceof Error ? error.message : 'Failed to send message. Please try again.',
        variant: 'destructive'
      });

      // Remove the loading message on error
      setMessages(prev => prev.slice(0, -1));
    } finally {
      setIsLoading(false);
    }
  };

  // =============================================
  // RENDER MINIMAL INTERFACE
  // =============================================
  return (
    <div className="h-full text-white flex flex-col relative">
      {/* Main Content - Centered Chat Interface */}
      <div className="flex-1 flex items-center justify-center px-8">
        <div className="max-w-2xl w-full">

          {/* Welcome Question with Icon - Positioned on left like the image */}
          {messages.length === 0 && (
            <div className="flex items-center gap-3 mb-8">
              <div className="flex-shrink-0">
                <img
                  src="http://thecodingkid.oyosite.com/logo_only.png"
                  alt="Osis Logo"
                  className="w-6 h-6 object-contain"
                />
              </div>
              <div>
                <WelcomeHeading
                  text="What are we analyzing today?"
                  className="text-xl font-normal text-white/90 font-sans tracking-tight leading-relaxed"
                  speed={80}
                />
              </div>
            </div>
          )}

          {/* Writing Area - No boxes, just text flow */}
          <div className="space-y-6">
            {/* Messages flow naturally like a document */}
            {messages.map((msg, index) => (
              <div key={index} className="space-y-4 border-b border-white/10 pb-6 last:border-b-0 last:pb-0">
                {msg.role === 'user' ? (
                  <div className="text-left">
                    <p className="text-white text-base leading-relaxed">{msg.content.text}</p>
                  </div>
                ) : msg.content.loadingPlaceholder ? (
                  <div className="text-left py-4">
                    <div className="space-y-3">
                      <div className="flex items-center gap-2">
                        <div className="w-2 h-2 bg-white/60 rounded-full animate-falling-bright"></div>
                        <span className="text-white/60 text-base">Loading</span>
                      </div>
                      <div className="flex items-center gap-2">
                        <div className="w-2 h-2 bg-white/60 rounded-full animate-falling-bright" style={{ animationDelay: '1s' }}></div>
                        <span className="text-white/60 text-base">Fetching</span>
                      </div>
                    </div>
                  </div>
                ) : (
                  <div className="text-left">
                    <MinimalLoadingMessage
                      userQuery={messages[index - 1]?.content?.text || ""}
                      isComplete={!msg.content.isLoading}
                      symbols={msg.content.symbols}
                      finalContent={msg.content.aiAnalysis || msg.content.text}
                      marketData={msg.content.marketData}
                      isLoading={msg.content.isLoading}
                      symbolTypes={msg.content.symbolTypes}
                      isGeneralMessage={msg.content.isGeneralMessage}
                      onRegenerate={async () => {
                        // Regeneration logic here
                      }}
                    />
                  </div>
                )}
              </div>
            ))}

            {/* Chat Input Box - Proper box with height like the image */}
            <div className="mt-8">
              <form onSubmit={handleSubmit}>
                <div className="relative">
                  <div className="border border-white/20 rounded-xl bg-transparent hover:border-white/30 focus-within:border-white/40 transition-colors duration-200 min-h-[120px]">
                    <textarea
                      ref={textareaRef}
                      className="w-full h-full bg-transparent border-none text-white text-base resize-none focus:outline-none leading-relaxed placeholder:text-white/40 px-4 py-4 rounded-xl"
                      value={message}
                      onChange={(e) => {
                        setMessage(e.target.value);
                      }}
                      onKeyDown={(e) => {
                        if (e.key === 'Enter' && !e.shiftKey) {
                          e.preventDefault();
                          handleSubmit(e as any);
                        } else if (e.key === 'Tab') {
                          e.preventDefault();
                          handleSubmit(e as any);
                        }
                      }}
                      onFocus={() => {
                        setIsTextareaFocused(true);
                      }}
                      onBlur={() => {
                        setIsTextareaFocused(false);
                      }}
                      rows={4}
                      style={{ minHeight: '120px', maxHeight: '200px' }}
                      disabled={isLoading}
                      placeholder=""
                    />

                    {/* Animated placeholder inside the chat box */}
                    {!message && !isLoading && !isTextareaFocused && !isAutoTyping && (
                      <div className="absolute left-4 top-4 pointer-events-none">
                        <TypingPlaceholder
                          examples={messages.length === 0 ? placeholderExamples : ["Continue the conversation..."]}
                          className="text-white/40 text-base"
                          shouldStop={message.length > 0 || isTextareaFocused || isAutoTyping}
                        />
                      </div>
                    )}

                    {/* Command button for auto-typing placeholder questions */}
                    {!message && !isLoading && !isTextareaFocused && !isAutoTyping && currentPlaceholderText && currentPlaceholderText.length > 3 && (
                      <button
                        type="button"
                        onClick={() => autoTypeText(currentPlaceholderText)}
                        className="absolute right-3 bottom-3 border border-white/30 hover:border-white/50 bg-transparent hover:bg-white/10 text-white/70 hover:text-white px-3 py-1 rounded-md text-xs font-medium shadow-sm transition-all duration-200"
                      >
                        ⌘ Type
                      </button>
                    )}
                  </div>
                </div>
              </form>

              {/* Clean loading state below input */}
              {isLoading && (
                <div className="mt-6 space-y-3">
                  <div className="flex items-center gap-2">
                    <div className="w-2 h-2 bg-white/60 rounded-full animate-falling-bright"></div>
                    <span className="text-white/60 text-base">Loading</span>
                  </div>
                  <div className="flex items-center gap-2">
                    <div className="w-2 h-2 bg-white/60 rounded-full animate-falling-bright" style={{ animationDelay: '1s' }}></div>
                    <span className="text-white/60 text-base">Fetching</span>
                  </div>
                </div>
              )}
            </div>
          </div>
        </div>
      </div>

      {/* Horizontal Chat History - Below Chat Interface */}
      {showHistory && (
        <div className="border-t border-white/[0.04] bg-black/10 px-8 py-6">
          <div className="max-w-6xl mx-auto">
            {/* Header */}
            <div className="flex items-center justify-between mb-6">
              <h3 className="text-sm text-white/60 font-sans tracking-wide">Osis History</h3>
              <div className="flex items-center gap-4">
                <button
                  onClick={createNewChat}
                  className="text-white/40 hover:text-white/70 transition-colors duration-200"
                >
                  <Plus className="w-4 h-4" />
                </button>
                <button
                  onClick={() => setShowHistory(false)}
                  className="text-white/30 hover:text-white/50 transition-colors duration-200 text-xs font-sans tracking-wide"
                >
                  Hide
                </button>
              </div>
            </div>

            {/* Horizontal Chat List */}
            {isLoadingHistory ? (
              <div className="flex items-center justify-center py-8">
                <div className="w-4 h-4 border border-white/20 border-t-white/40 rounded-full animate-spin"></div>
              </div>
            ) : chatHistory.length === 0 ? (
              <div className="text-center py-8">
                <p className="text-white/40 text-sm font-sans mb-1">No conversations yet</p>
                <p className="text-white/30 text-xs font-sans">Your chat history will appear here</p>
              </div>
            ) : (
              <div className="flex gap-3 overflow-x-auto pb-2">
                {chatHistory.map((chat) => (
                  <div
                    key={chat.id}
                    onClick={() => navigate(`/chat/${chat.id}`)}
                    className={`group cursor-pointer flex-shrink-0 w-48 p-4 rounded-xl transition-all duration-200 ${
                      currentChatId === chat.id
                        ? 'bg-white/[0.08] border border-white/[0.12]'
                        : 'bg-white/[0.02] border border-white/[0.04] hover:bg-white/[0.04] hover:border-white/[0.08]'
                    }`}
                  >
                    <div className="flex items-start justify-between mb-2">
                      <h4 className={`text-sm font-sans truncate leading-tight ${
                        currentChatId === chat.id ? 'text-white' : 'text-white/80 group-hover:text-white'
                      }`}>
                        {chat.title}
                      </h4>
                      <button
                        onClick={(e) => {
                          e.stopPropagation();
                          deleteChat(chat.id);
                        }}
                        className="opacity-0 group-hover:opacity-100 ml-2 text-white/30 hover:text-white/60 transition-all duration-200"
                      >
                        <Trash2 className="w-3 h-3" />
                      </button>
                    </div>
                    <p className="text-xs text-white/40 font-sans">
                      {formatChatTime(chat.updated_at)}
                    </p>
                  </div>
                ))}
              </div>
            )}
          </div>
        </div>
      )}

      {/* Show History Button - When Hidden */}
      {!showHistory && (
        <div className="border-t border-white/[0.04] px-8 py-4">
          <div className="max-w-6xl mx-auto text-center">
            <button
              onClick={() => setShowHistory(true)}
              className="text-white/30 hover:text-white/50 transition-colors duration-200 text-xs font-sans tracking-wide"
            >
              Show History
            </button>
          </div>
        </div>
      )}
    </div>
  );
};

export default MinimalChatInterface;
