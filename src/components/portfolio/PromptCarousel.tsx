import React, { useState, useEffect } from 'react';
import {
  <PERSON><PERSON>dingUp,
  Lightbulb,
  Sparkles,
  Brain,
  Rocket,
  Briefcase,
  Globe,
  Target,
  Zap,
  DollarSign,
  Gamepad2,
  Music,
  Building,
  Pill,
  TrendingDown,
  Gem,
  Dumbbell,
  Wheat,
  CreditCard,
  Copyright,
  CloudRain,
  Bot,
  HeartPulse,
  Lock,
  Scissors
} from 'lucide-react';

// Define the prompt categories and their items
const PORTFOLIO_PROMPTS = [
  // Tech & Future
  {
    text: "Companies building metaverse infrastructure",
    category: "Tech & Future",
    icon: <Rocket className="w-4 h-4 text-blue-400" />
  },
  {
    text: "AI companies that other AIs actually use",
    category: "Tech & Future",
    icon: <Brain className="w-4 h-4 text-purple-400" />
  },
  {
    text: "Energy storage beyond lithium batteries",
    category: "Tech & Future",
    icon: <Zap className="w-4 h-4 text-yellow-400" />
  },
  {
    text: "Space infrastructure not just tourism",
    category: "Tech & Future",
    icon: <Rocket className="w-4 h-4 text-blue-400" />
  },
  {
    text: "Quantum computing supply chain plays",
    category: "Tech & Future",
    icon: <Sparkles className="w-4 h-4 text-indigo-400" />
  },

  // Culture & Society
  {
    text: "Gaming addiction recovery businesses",
    category: "Culture & Society",
    icon: <Gamepad2 className="w-4 h-4 text-green-400" />
  },
  {
    text: "Brands Gen-Alpha will obsess over in 2030",
    category: "Culture & Society",
    icon: <Target className="w-4 h-4 text-red-400" />
  },
  {
    text: "Tech companies solving the housing crisis",
    category: "Culture & Society",
    icon: <Building className="w-4 h-4 text-amber-400" />
  },
  {
    text: "Music industry disruptors beyond streaming",
    category: "Culture & Society",
    icon: <Music className="w-4 h-4 text-pink-400" />
  },
  {
    text: "Psychedelic medicine commercialization",
    category: "Culture & Society",
    icon: <Pill className="w-4 h-4 text-violet-400" />
  },

  // Contrarian & Edgy
  {
    text: "Companies that thrive during recessions",
    category: "Contrarian & Edgy",
    icon: <TrendingDown className="w-4 h-4 text-red-400" />
  },
  {
    text: "Unsexy businesses with monopolistic moats",
    category: "Contrarian & Edgy",
    icon: <Briefcase className="w-4 h-4 text-gray-400" />
  },
  {
    text: "Death tech and digital afterlife companies",
    category: "Contrarian & Edgy",
    icon: <Globe className="w-4 h-4 text-slate-400" />
  },
  {
    text: "Anti-ESG politically incorrect profit machines",
    category: "Contrarian & Edgy",
    icon: <DollarSign className="w-4 h-4 text-green-400" />
  },
  {
    text: "Companies betting against remote work",
    category: "Contrarian & Edgy",
    icon: <Building className="w-4 h-4 text-amber-400" />
  },

  // Lifestyle & Trends
  {
    text: "Luxury brands for crypto millionaires",
    category: "Lifestyle & Trends",
    icon: <Gem className="w-4 h-4 text-cyan-400" />
  },
  {
    text: "Fitness tech for the chronically online",
    category: "Lifestyle & Trends",
    icon: <Dumbbell className="w-4 h-4 text-emerald-400" />
  },
  {
    text: "Alternative protein beyond fake meat",
    category: "Lifestyle & Trends",
    icon: <Wheat className="w-4 h-4 text-amber-400" />
  },
  {
    text: "Financial services for the unbanked globally",
    category: "Lifestyle & Trends",
    icon: <CreditCard className="w-4 h-4 text-blue-400" />
  },
  {
    text: "IP ownership in the creator economy",
    category: "Lifestyle & Trends",
    icon: <Copyright className="w-4 h-4 text-purple-400" />
  },

  // Wild Cards
  {
    text: "Companies preparing for climate migration",
    category: "Wild Cards",
    icon: <CloudRain className="w-4 h-4 text-blue-400" />
  },
  {
    text: "Robot insurance and liability plays",
    category: "Wild Cards",
    icon: <Bot className="w-4 h-4 text-gray-400" />
  },
  {
    text: "Longevity tech for billionaires",
    category: "Wild Cards",
    icon: <HeartPulse className="w-4 h-4 text-red-400" />
  },
  {
    text: "Privacy-as-a-service businesses",
    category: "Wild Cards",
    icon: <Lock className="w-4 h-4 text-indigo-400" />
  },
  {
    text: "Divorce economy beneficiaries",
    category: "Wild Cards",
    icon: <Scissors className="w-4 h-4 text-pink-400" />
  }
];

interface PromptCarouselProps {
  onPromptSelect: (prompt: string) => void;
}

const PromptCarousel: React.FC<PromptCarouselProps> = ({ onPromptSelect }) => {
  const [slidePosition, setSlidePosition] = useState(0);
  const [carouselWidth, setCarouselWidth] = useState(0);

  // Initialize and manage the carousel animation
  useEffect(() => {
    let animationInterval: NodeJS.Timeout;

    const initializeCarousel = () => {
      const carousel = document.querySelector('.portfolio-prompts-carousel');
      if (carousel) {
        const totalWidth = carousel.scrollWidth / 2; // Divide by 2 because we duplicated the items
        setCarouselWidth(totalWidth);

        // Start the animation with a moderate speed
        const animationSpeed = 20; // Lower = faster
        animationInterval = setInterval(() => {
          setSlidePosition(prev => {
            const newPosition = prev - 1;
            return newPosition <= -totalWidth ? 0 : newPosition;
          });
        }, animationSpeed);
      }
    };

    // Initialize carousel
    initializeCarousel();

    // Set up a mutation observer to watch for changes to the carousel
    const observer = new MutationObserver(() => {
      // Clear existing interval
      if (animationInterval) {
        clearInterval(animationInterval);
      }
      // Reinitialize carousel
      initializeCarousel();
    });

    const carouselElement = document.querySelector('.portfolio-prompts-carousel');
    if (carouselElement) {
      observer.observe(carouselElement, {
        childList: true,
        subtree: true,
        attributes: true
      });
    }

    // Cleanup function
    return () => {
      if (animationInterval) {
        clearInterval(animationInterval);
      }
      observer.disconnect();
    };
  }, []);

  return (
    <div className="mt-6 mb-6">
      <div className="flex overflow-hidden py-2">
        <div
          className="flex gap-3 portfolio-prompts-carousel"
          style={{
            transform: `translateX(${slidePosition}px)`,
            transition: slidePosition === 0 ? 'none' : 'transform 0.1s linear',
            willChange: 'transform'
          }}
        >
          {/* Duplicate the prompts to create a seamless loop */}
          {[...PORTFOLIO_PROMPTS, ...PORTFOLIO_PROMPTS].map((prompt, index) => (
            <button
              key={index}
              onClick={() => onPromptSelect(prompt.text)}
              className="text-white/50 hover:text-white/80 text-sm font-sans transition-colors duration-200 whitespace-nowrap"
            >
              {prompt.text}
            </button>
          ))}
        </div>
      </div>

      {/* Clean fade-out effect */}
      <div className="absolute inset-y-0 right-0 w-24 pointer-events-none">
        <div className="absolute inset-0 bg-gradient-to-l from-[#0A0A0A] to-transparent"></div>
      </div>
    </div>
  );
};

export default PromptCarousel;
