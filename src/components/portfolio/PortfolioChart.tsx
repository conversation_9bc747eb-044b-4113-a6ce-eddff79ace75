import React, { useState, useRef, useCallback } from 'react';
import ReactECharts from 'echarts-for-react';

interface ChartDataPoint {
  date: string;
  value: number;
}

interface PortfolioChartProps {
  data: ChartDataPoint[];
  title?: string;
  height?: number;
  loading?: boolean;
  projectionData?: ChartDataPoint[];
  timeframe?: string;
  showTrackingDot?: boolean;
}

const PortfolioChart: React.FC<PortfolioChartProps> = ({
  data,
  title = 'Portfolio Performance',
  height = 300,
  loading = false,
  projectionData = [],
  timeframe = '1Y', // eslint-disable-line @typescript-eslint/no-unused-vars
  showTrackingDot = true
}) => {
  const chartRef = useRef<any>(null);
  const [trackingDot, setTrackingDot] = useState<{
    visible: boolean;
    x: number;
    y: number;
    value: number;
    date: string;
    isPositive: boolean;
  }>({
    visible: false,
    x: 0,
    y: 0,
    value: 0,
    date: '',
    isPositive: true
  });
  if (loading) {
    return (
      <div className="w-full flex items-center justify-center bg-[#0D0D0D] rounded-lg" style={{ height: `${height}px` }}>
        <div className="animate-pulse flex flex-col items-center">
          <div className="h-8 w-8 rounded-full border-2 border-t-transparent border-white/30 animate-spin"></div>
          <p className="mt-2 text-sm text-white/60">Loading chart data...</p>
        </div>
      </div>
    );
  }

  if (!data || data.length === 0) {
    return (
      <div className="w-full flex items-center justify-center bg-[#0D0D0D] rounded-lg" style={{ height: `${height}px` }}>
        <p className="text-white/60">No data available</p>
      </div>
    );
  }

  // Combine historical and projection data
  const allData = [...data, ...projectionData];
  const dates = allData.map(item => item.date);
  const values = allData.map(item => item.value);

  // Historical data only for percentage calculation
  const historicalValues = data.map(item => item.value);
  const startValue = historicalValues[0];
  const currentValue = historicalValues[historicalValues.length - 1];
  const percentChange = ((currentValue - startValue) / startValue) * 100;

  // Calculate min and max values for y-axis scaling (including projections)
  const minValue = Math.min(...values) * 0.99;
  const maxValue = Math.max(...values) * 1.01;

  // Handle mouse events for tracking dot
  const handleChartReady = useCallback((chartInstance: any) => {
    chartRef.current = chartInstance;

    if (!showTrackingDot) return;

    // Get the chart container
    const chartDom = chartInstance.getDom();

    // Mouse move handler
    const handleMouseMove = (event: MouseEvent) => {
      const rect = chartDom.getBoundingClientRect();
      const mouseX = event.clientX - rect.left;

      // Account for chart margins/padding
      const chartMargin = 24;
      const effectiveChartWidth = rect.width - (chartMargin * 2);
      const adjustedMouseX = mouseX - chartMargin;

      // Calculate progress (0 to 1) across the effective chart area
      const progress = Math.max(0, Math.min(adjustedMouseX / effectiveChartWidth, 1));

      // Map to data index with better precision
      const exactIndex = progress * (allData.length - 1);
      const dataIndex = Math.round(exactIndex);
      const clampedIndex = Math.max(0, Math.min(dataIndex, allData.length - 1));
      const dataPoint = allData[clampedIndex];

      // Debug logging to see what's happening
      console.log('Mouse tracking:', {
        mouseX,
        adjustedMouseX,
        progress: progress.toFixed(3),
        dataIndex,
        clampedIndex,
        totalDataPoints: allData.length,
        dataPoint: dataPoint ? { date: dataPoint.date, value: dataPoint.value } : null
      });

      if (dataPoint) {
        // Position dot exactly where the data point should be on the line
        const dotX = chartMargin + (clampedIndex / (allData.length - 1)) * effectiveChartWidth;

        // Calculate Y position based on the value with better scaling
        const valueRange = maxValue - minValue;
        const valueProgress = (dataPoint.value - minValue) / valueRange;
        const chartTopMargin = 100;
        const chartBottomMargin = 50;
        const effectiveChartHeight = rect.height - chartTopMargin - chartBottomMargin;
        const dotY = chartTopMargin + effectiveChartHeight * (1 - valueProgress);

        setTrackingDot({
          visible: true,
          x: dotX,
          y: dotY,
          value: dataPoint.value,
          date: dataPoint.date,
          isPositive: percentChange >= 0
        });
      }
    };

    // Mouse leave handler
    const handleMouseLeave = () => {
      setTrackingDot(prev => ({ ...prev, visible: false }));
    };

    // Add event listeners
    chartDom.addEventListener('mousemove', handleMouseMove);
    chartDom.addEventListener('mouseleave', handleMouseLeave);

    // Cleanup function
    return () => {
      chartDom.removeEventListener('mousemove', handleMouseMove);
      chartDom.removeEventListener('mouseleave', handleMouseLeave);
    };
  }, [allData, percentChange, showTrackingDot, maxValue, minValue]);

  const option = {
    backgroundColor: 'transparent',
    title: [
      {
        text: title || 'Agent Returns',
        left: '24px',
        top: '24px',
        textStyle: {
          color: 'rgba(255, 255, 255, 0.9)',
          fontWeight: 400,
          fontSize: 24,
          fontFamily: 'sans-serif'
        }
      },
      {
        text: `${percentChange >= 0 ? '+' : ''}${percentChange.toFixed(2)}%`,
        left: '24px',
        top: '60px',
        textStyle: {
          color: percentChange >= 0 ? 'rgba(50, 250, 154, 0.95)' : 'rgba(229, 128, 128, 0.95)',
          fontWeight: 400,
          fontSize: 32,
          fontFamily: 'sans-serif'
        }
      }
    ],
    tooltip: {
      trigger: 'axis' as const,
      axisPointer: {
        type: 'line',
        axis: 'x',
        lineStyle: {
          color: 'rgba(255, 255, 255, 0.3)',
          width: 1,
          type: 'dashed'
        }
      },
      formatter: function(params: any) {
        const dataIndex = params[0].dataIndex;
        const date = dates[dataIndex];

        // Format date to be more readable
        const formattedDate = new Date(date).toLocaleDateString('en-US', {
          year: 'numeric',
          month: 'short',
          day: 'numeric'
        });

        return `
          <div style="
            background-color: rgba(30, 30, 30, 0.95); /* Darker background */
            border: 1px solid rgba(255, 255, 255, 0.2); /* Slightly more prominent border */
            border-radius: 18px; /* Match image curves */
            padding: 3px 10px; /* Adjust padding */
            color: #fff;
            font-size: 11px; /* Smaller font for date */
            font-weight: 500;
            text-align: center;
          ">
            ${formattedDate}
          </div>
        `;
      },
      backgroundColor: 'transparent',
      borderColor: 'transparent',
      textStyle: {
        color: '#fff'
      },
      extraCssText: 'box-shadow: none;',
      confine: true
    },
    grid: {
      left: '0%',
      right: '0%',
      bottom: '0%',
      top: 100,
      containLabel: false
    },
    xAxis: {
      type: 'category' as const,
      data: dates,
      show: false,
      boundaryGap: false,
      axisLine: { show: false },
      axisTick: { show: false },
      splitLine: { show: false }
    },
    yAxis: {
      type: 'value' as const,
      min: minValue,
      max: maxValue,
      show: false,
      axisLine: { show: false },
      axisTick: { show: false },
      splitLine: { show: false }
    },
    series: [
      // Historical data with area fill - extends across full width
      {
        data: values, // Use all values (historical + projection)
        type: 'line' as const,
        smooth: true,
        symbol: 'none',
        sampling: 'none' as const,
        lineStyle: {
          width: 2,
          color: percentChange >= 0 ? 'rgba(50, 250, 154, 0.9)' : 'rgba(229, 128, 128, 0.9)',
          type: 'solid'
        },
        emphasis: {
          focus: 'series',
          itemStyle: {
            color: 'rgba(50, 250, 154, 1)', // Bright green dot color on hover
            borderColor: 'rgba(50, 250, 154, 1)', // Bright green dot border color on hover
            borderWidth: 2,
            symbol: 'circle', // Ensure a dot symbol is used
            symbolSize: 8, // Slightly larger dot size
            shadowBlur: 10, // Add glow effect
            shadowColor: 'rgba(50, 250, 154, 0.8)' // Glow color
          },
          lineStyle: {
            color: percentChange >= 0 ? 'rgba(50, 250, 154, 0.9)' : 'rgba(229, 128, 128, 0.9)',
            width: 2,
            type: 'solid'
          }
        },
        areaStyle: {
          color: {
            type: 'linear' as const,
            x: 0,
            y: 0,
            x2: 0,
            y2: 1,
            colorStops: [{
              offset: 0,
              color: percentChange >= 0 ? 'rgba(50, 250, 154, 0.4)' : 'rgba(229, 128, 128, 0.4)'
            }, {
              offset: 0.8,
              color: percentChange >= 0 ? 'rgba(50, 250, 154, 0.15)' : 'rgba(229, 128, 128, 0.15)'
            }, {
              offset: 1,
              color: percentChange >= 0 ? 'rgba(50, 250, 154, 0.05)' : 'rgba(229, 128, 128, 0.05)'
            }]
          }
        },
        connectNulls: false,
        step: false as const
      }
    ]
  };

  // Ensure we have valid data for rendering
  if (!data || data.length === 0) {
    console.warn('PortfolioChart: No valid data provided');
    return (
      <div className="w-full flex items-center justify-center bg-[#0D0D0D] rounded-lg" style={{ height: `${height}px` }}>
        <p className="text-white/60">No chart data available</p>
      </div>
    );
  }

  return (
    <div className="w-full relative" style={{ height: `${height}px` }}>
      <ReactECharts
        option={option as any}
        style={{
          height: `${height}px`,
          width: '100%',
          backgroundColor: '#0D0D0D',
          minHeight: `${height}px`
        }}
        className="bg-[#0D0D0D] rounded-md w-full"
        onChartReady={handleChartReady}
        opts={{
          renderer: 'canvas',
          width: 'auto',
          height: height,
          devicePixelRatio: window.devicePixelRatio || 1
        }}
        notMerge={true}
        lazyUpdate={false}
      />

      {/* Tracking Dot Overlay */}
      {showTrackingDot && trackingDot.visible && (
        <>
          {/* Vertical Dashed Line */}
          <div
            className="absolute pointer-events-none z-5"
            style={{
              left: trackingDot.x,
              top: 100,
              bottom: 24,
              width: '1px',
              background: 'rgba(255, 255, 255, 0.3)',
              borderLeft: '1px dashed rgba(255, 255, 255, 0.3)'
            }}
          />

          {/* Tracking Dot */}
          <div
            className="absolute pointer-events-none z-10"
            style={{
              left: trackingDot.x - 6,
              top: trackingDot.y - 6,
              transform: 'translate(0, 0)'
            }}
          >
            <div
              className={`w-3 h-3 rounded-full border-2 border-white shadow-lg ${
                trackingDot.isPositive
                  ? 'bg-emerald-400 shadow-emerald-400/50'
                  : 'bg-red-400 shadow-red-400/50'
              }`}
              style={{
                boxShadow: `0 0 12px ${trackingDot.isPositive ? 'rgba(52, 211, 153, 0.6)' : 'rgba(248, 113, 113, 0.6)'}`
              }}
            />
          </div>

          {/* Tooltip */}
          <div
            className="absolute pointer-events-none z-20 bg-[#1A1A1A] border border-white/10 rounded-lg px-3 py-2 text-sm shadow-xl backdrop-blur-sm"
            style={{
              left: trackingDot.x + 15,
              top: trackingDot.y - 35,
              transform: trackingDot.x > window.innerWidth * 0.7 ? 'translateX(-100%)' : 'translateX(0)'
            }}
          >
            <div className="text-white font-medium">
              ${trackingDot.value.toLocaleString(undefined, { minimumFractionDigits: 2, maximumFractionDigits: 2 })}
            </div>
            <div className="text-white/60 text-xs">
              {new Date(trackingDot.date).toLocaleDateString()}
            </div>
          </div>
        </>
      )}
    </div>
  );
};

export default PortfolioChart;
