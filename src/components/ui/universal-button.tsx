import * as React from "react"
import { Slot } from "@radix-ui/react-slot"
import { cva, type VariantProps } from "class-variance-authority"
import { cn } from "@/lib/utils"

const universalButtonVariants = cva(
  "inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-lg text-sm font-medium transition-all duration-300 focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-emerald-500/20 focus-visible:ring-offset-2 focus-visible:ring-offset-[#0A0A0A] disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg]:size-4 [&_svg]:shrink-0 relative overflow-hidden group",
  {
    variants: {
      variant: {
        // Primary button with animated border and premium styling
        primary: [
          "bg-gradient-to-br from-[#1A1A1C]/90 to-[#141416]/90",
          "text-white/90 border border-white/[0.08]",
          "shadow-[inset_0_1px_0_0_rgba(255,255,255,0.05),0_2px_8px_rgba(0,0,0,0.3)]",
          "hover:shadow-[inset_0_1px_0_0_rgba(255,255,255,0.1),0_4px_16px_rgba(0,0,0,0.4)]",
          "hover:bg-gradient-to-br hover:from-[#1F1F21]/90 hover:to-[#191919]/90",
          "hover:border-white/[0.12] hover:text-white",
          "active:scale-[0.98] active:shadow-[inset_0_2px_4px_rgba(0,0,0,0.2)]",
          // Animated border effect
          "before:absolute before:inset-0 before:rounded-lg before:p-[1px]",
          "before:bg-gradient-to-r before:from-transparent before:via-emerald-500/20 before:to-transparent",
          "before:opacity-0 hover:before:opacity-100 before:transition-opacity before:duration-500",
          "before:-z-10"
        ],
        
        // Secondary button with glass effect
        secondary: [
          "bg-white/[0.04] backdrop-blur-sm border border-white/[0.08]",
          "text-white/70 hover:text-white/90",
          "shadow-[inset_0_1px_0_0_rgba(255,255,255,0.03)]",
          "hover:bg-white/[0.08] hover:border-white/[0.12]",
          "hover:shadow-[inset_0_1px_0_0_rgba(255,255,255,0.06)]",
          "active:scale-[0.98]"
        ],
        
        // Destructive button with red theme
        destructive: [
          "bg-gradient-to-br from-red-900/20 to-red-950/30",
          "text-red-400 border border-red-500/20",
          "shadow-[inset_0_1px_0_0_rgba(255,255,255,0.03)]",
          "hover:bg-gradient-to-br hover:from-red-900/30 hover:to-red-950/40",
          "hover:text-red-300 hover:border-red-500/30",
          "active:scale-[0.98]"
        ],
        
        // Ghost button for minimal actions
        ghost: [
          "text-white/70 hover:text-white hover:bg-white/[0.04]",
          "hover:shadow-[inset_0_1px_0_0_rgba(255,255,255,0.03)]",
          "active:scale-[0.98]"
        ],
        
        // Success button with emerald theme
        success: [
          "bg-gradient-to-br from-emerald-900/20 to-emerald-950/30",
          "text-emerald-400 border border-emerald-500/20",
          "shadow-[inset_0_1px_0_0_rgba(255,255,255,0.03)]",
          "hover:bg-gradient-to-br hover:from-emerald-900/30 hover:to-emerald-950/40",
          "hover:text-emerald-300 hover:border-emerald-500/30",
          "active:scale-[0.98]"
        ]
      },
      size: {
        sm: "h-8 px-3 text-xs rounded-md",
        default: "h-10 px-4 py-2",
        lg: "h-12 px-6 text-base rounded-xl",
        xl: "h-14 px-8 text-lg rounded-xl",
        icon: "h-10 w-10",
        "icon-sm": "h-8 w-8",
        "icon-lg": "h-12 w-12"
      },
      animation: {
        none: "",
        glow: [
          "after:absolute after:inset-0 after:rounded-lg after:opacity-0",
          "after:bg-gradient-to-r after:from-emerald-500/10 after:via-emerald-400/20 after:to-emerald-500/10",
          "hover:after:opacity-100 after:transition-opacity after:duration-500",
          "after:animate-pulse"
        ],
        shimmer: [
          "before:absolute before:inset-0 before:rounded-lg",
          "before:bg-gradient-to-r before:from-transparent before:via-white/10 before:to-transparent",
          "before:translate-x-[-100%] hover:before:translate-x-[100%]",
          "before:transition-transform before:duration-700 before:ease-in-out"
        ]
      }
    },
    defaultVariants: {
      variant: "primary",
      size: "default",
      animation: "none"
    }
  }
)

export interface UniversalButtonProps
  extends React.ButtonHTMLAttributes<HTMLButtonElement>,
    VariantProps<typeof universalButtonVariants> {
  asChild?: boolean
  loading?: boolean
  loadingText?: string
}

const UniversalButton = React.forwardRef<HTMLButtonElement, UniversalButtonProps>(
  ({ 
    className, 
    variant, 
    size, 
    animation,
    asChild = false, 
    loading = false,
    loadingText,
    children,
    disabled,
    ...props 
  }, ref) => {
    const Comp = asChild ? Slot : "button"
    
    return (
      <Comp
        className={cn(universalButtonVariants({ variant, size, animation, className }))}
        ref={ref}
        disabled={disabled || loading}
        {...props}
      >
        {loading ? (
          <>
            <div className="w-4 h-4 border-2 border-current border-t-transparent rounded-full animate-spin" />
            {loadingText || "Loading..."}
          </>
        ) : (
          children
        )}
      </Comp>
    )
  }
)

UniversalButton.displayName = "UniversalButton"

export { UniversalButton, universalButtonVariants }
