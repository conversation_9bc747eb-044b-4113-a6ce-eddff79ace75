import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import {
  MessageCircle,
  TrendingUp,
  Briefcase,
  Hammer,
  Bot,
  Activity,
  ArrowRight,
  BarChart3,
  Zap,
  Target,
  Command,
  Trophy,
  Star,
  CheckCircle,
  Clock,
  Users,
  DollarSign,
  Percent,
  Calendar,
  Award,
  Sparkles,
  ChevronRight,
  Play,
  Search,
  TestTube
} from 'lucide-react';
import { Button } from '@/components/ui/button';

const Home: React.FC = () => {
  const navigate = useNavigate();
  const [currentTime, setCurrentTime] = useState(new Date());

  useEffect(() => {
    const timer = setInterval(() => setCurrentTime(new Date()), 1000);
    return () => clearInterval(timer);
  }, []);

  // Mock data - in real app this would come from API
  const accomplishments = [
    {
      id: 1,
      title: 'First Agent Created',
      description: 'Built your first trading agent',
      icon: Trophy,
      completed: true,
      date: '2 days ago',
      type: 'milestone'
    },
    {
      id: 2,
      title: 'Portfolio Optimized',
      description: 'Successfully optimized a portfolio',
      icon: Star,
      completed: true,
      date: '1 day ago',
      type: 'achievement'
    },
    {
      id: 3,
      title: 'Market Scanner Expert',
      description: 'Complete your first market scan',
      icon: Target,
      completed: false,
      date: null,
      type: 'challenge'
    }
  ];

  const agentPerformance = [
    {
      name: 'Growth Agent Alpha',
      status: 'active',
      performance: '+12.4%',
      trades: 23,
      winRate: 78,
      lastActive: '2 min ago'
    },
    {
      name: 'Value Hunter Beta',
      status: 'testing',
      performance: '+8.7%',
      trades: 15,
      winRate: 67,
      lastActive: '1 hour ago'
    }
  ];

  const nextActions = [
    {
      title: 'Run Your First Market Scan',
      description: 'Discover opportunities across 5,000+ stocks',
      icon: Search,
      action: () => navigate('/agent-scanner'),
      priority: 'high',
      estimatedTime: '2 min'
    },
    {
      title: 'Test Your Strategy',
      description: 'Backtest your trading ideas with historical data',
      icon: TestTube,
      action: () => navigate('/agent-backtesting'),
      priority: 'medium',
      estimatedTime: '5 min'
    }
  ];

  return (
    <div className="h-full bg-[#0A0A0A] text-white overflow-y-auto">
      {/* Navigator Buttons - Top Right */}
      <div className="absolute top-6 right-6 z-10 flex gap-3">
        <button
          onClick={() => navigate('/agent-builder')}
          className="flex items-center gap-2 bg-white hover:bg-white/95 text-black px-4 py-2 rounded-lg text-sm font-medium shadow-sm hover:shadow-md transition-all duration-200 font-sans"
        >
          <Hammer className="w-4 h-4" />
          <span>Build</span>
        </button>
        <button
          onClick={() => navigate('/agent-backtesting')}
          className="flex items-center gap-2 bg-white/[0.08] hover:bg-white/[0.12] text-white px-4 py-2 rounded-lg text-sm font-medium border border-white/[0.08] transition-all duration-200 font-sans"
        >
          <TestTube className="w-4 h-4" />
          <span>Test</span>
        </button>
        <button
          onClick={() => navigate('/agent-scanner')}
          className="flex items-center gap-2 bg-white/[0.08] hover:bg-white/[0.12] text-white px-4 py-2 rounded-lg text-sm font-medium border border-white/[0.08] transition-all duration-200 font-sans"
        >
          <Search className="w-4 h-4" />
          <span>Scan</span>
        </button>
      </div>

      <div className="max-w-7xl mx-auto px-8 py-12">
        {/* Header */}
        <div className="mb-12">
          <div className="flex items-center justify-between mb-6">
            <div>
              <h1 className="text-4xl font-normal text-white mb-2 font-sans">
                Trading Headquarters
              </h1>
              <p className="text-white/60 text-lg font-sans">
                Welcome back, Trader • {currentTime.toLocaleDateString()} • {currentTime.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })}
              </p>
            </div>
            <div className="flex items-center gap-4">
              <div className="flex items-center gap-2 px-3 py-1.5 bg-emerald-500/10 border border-emerald-500/20 rounded-lg">
                <div className="w-2 h-2 bg-emerald-500 rounded-full animate-pulse"></div>
                <span className="text-emerald-400 text-sm font-medium font-sans">2 Agents Active</span>
              </div>
            </div>
          </div>
        </div>

        {/* Main Grid */}
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          {/* Left Column - Accomplishments & Performance */}
          <div className="lg:col-span-2 space-y-8">
            {/* Accomplishments */}
            <div className="bg-white/[0.02] border border-white/[0.06] rounded-xl p-6">
              <div className="flex items-center gap-3 mb-6">
                <div className="w-8 h-8 bg-yellow-500/20 border border-yellow-500/30 rounded-lg flex items-center justify-center">
                  <Trophy className="w-4 h-4 text-yellow-400" />
                </div>
                <h2 className="text-xl font-medium text-white font-sans">Recent Accomplishments</h2>
              </div>

              <div className="space-y-4">
                {accomplishments.map((achievement) => {
                  const Icon = achievement.icon;
                  return (
                    <div key={achievement.id} className="flex items-center gap-4 p-4 bg-white/[0.02] border border-white/[0.04] rounded-lg">
                      <div className={`w-10 h-10 rounded-lg flex items-center justify-center ${
                        achievement.completed
                          ? 'bg-emerald-500/20 border border-emerald-500/30'
                          : 'bg-white/[0.04] border border-white/[0.08]'
                      }`}>
                        {achievement.completed ? (
                          <CheckCircle className="w-5 h-5 text-emerald-400" />
                        ) : (
                          <Icon className="w-5 h-5 text-white/60" />
                        )}
                      </div>
                      <div className="flex-1">
                        <h3 className="text-sm font-medium text-white font-sans">{achievement.title}</h3>
                        <p className="text-xs text-white/60 font-sans">{achievement.description}</p>
                      </div>
                      <div className="text-right">
                        {achievement.completed ? (
                          <div className="flex items-center gap-1 text-emerald-400">
                            <CheckCircle className="w-3 h-3" />
                            <span className="text-xs font-sans">{achievement.date}</span>
                          </div>
                        ) : (
                          <div className="flex items-center gap-1 text-white/40">
                            <Clock className="w-3 h-3" />
                            <span className="text-xs font-sans">Pending</span>
                          </div>
                        )}
                      </div>
                    </div>
                  );
                })}
              </div>
            </div>

            {/* Agent Performance */}
            <div className="bg-white/[0.02] border border-white/[0.06] rounded-xl p-6">
              <div className="flex items-center gap-3 mb-6">
                <div className="w-8 h-8 bg-blue-500/20 border border-blue-500/30 rounded-lg flex items-center justify-center">
                  <BarChart3 className="w-4 h-4 text-blue-400" />
                </div>
                <h2 className="text-xl font-medium text-white font-sans">Agent Performance</h2>
              </div>

              <div className="space-y-4">
                {agentPerformance.map((agent, index) => (
                  <div key={index} className="p-4 bg-white/[0.02] border border-white/[0.04] rounded-lg">
                    <div className="flex items-center justify-between mb-3">
                      <div className="flex items-center gap-3">
                        <div className={`w-3 h-3 rounded-full ${
                          agent.status === 'active' ? 'bg-emerald-500' : 'bg-yellow-500'
                        }`}></div>
                        <h3 className="text-sm font-medium text-white font-sans">{agent.name}</h3>
                        <span className={`px-2 py-0.5 rounded text-xs font-medium font-sans ${
                          agent.status === 'active'
                            ? 'bg-emerald-500/20 text-emerald-400'
                            : 'bg-yellow-500/20 text-yellow-400'
                        }`}>
                          {agent.status}
                        </span>
                      </div>
                      <div className="text-right">
                        <div className="text-sm font-medium text-emerald-400 font-sans">{agent.performance}</div>
                        <div className="text-xs text-white/60 font-sans">{agent.lastActive}</div>
                      </div>
                    </div>

                    <div className="grid grid-cols-2 gap-4 text-xs">
                      <div className="flex items-center gap-2">
                        <TrendingUp className="w-3 h-3 text-white/60" />
                        <span className="text-white/60 font-sans">Trades: </span>
                        <span className="text-white font-sans">{agent.trades}</span>
                      </div>
                      <div className="flex items-center gap-2">
                        <Percent className="w-3 h-3 text-white/60" />
                        <span className="text-white/60 font-sans">Win Rate: </span>
                        <span className="text-white font-sans">{agent.winRate}%</span>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </div>

          {/* Right Column - Next Actions & Quick Stats */}
          <div className="space-y-8">
            {/* Next Actions */}
            <div className="bg-white/[0.02] border border-white/[0.06] rounded-xl p-6">
              <div className="flex items-center gap-3 mb-6">
                <div className="w-8 h-8 bg-purple-500/20 border border-purple-500/30 rounded-lg flex items-center justify-center">
                  <Sparkles className="w-4 h-4 text-purple-400" />
                </div>
                <h2 className="text-xl font-medium text-white font-sans">Recommended Actions</h2>
              </div>

              <div className="space-y-4">
                {nextActions.map((action, index) => {
                  const Icon = action.icon;
                  return (
                    <div key={index} className="p-4 bg-white/[0.02] border border-white/[0.04] rounded-lg hover:border-white/[0.08] transition-colors duration-200 cursor-pointer" onClick={action.action}>
                      <div className="flex items-start gap-3">
                        <div className={`w-8 h-8 rounded-lg flex items-center justify-center ${
                          action.priority === 'high'
                            ? 'bg-orange-500/20 border border-orange-500/30'
                            : 'bg-blue-500/20 border border-blue-500/30'
                        }`}>
                          <Icon className={`w-4 h-4 ${
                            action.priority === 'high' ? 'text-orange-400' : 'text-blue-400'
                          }`} />
                        </div>
                        <div className="flex-1">
                          <div className="flex items-center gap-2 mb-1">
                            <h3 className="text-sm font-medium text-white font-sans">{action.title}</h3>
                            {action.priority === 'high' && (
                              <span className="px-1.5 py-0.5 bg-orange-500/20 text-orange-400 text-xs font-medium rounded font-sans">
                                High Priority
                              </span>
                            )}
                          </div>
                          <p className="text-xs text-white/60 font-sans mb-2">{action.description}</p>
                          <div className="flex items-center gap-2">
                            <Clock className="w-3 h-3 text-white/40" />
                            <span className="text-xs text-white/40 font-sans">{action.estimatedTime}</span>
                            <ChevronRight className="w-3 h-3 text-white/40 ml-auto" />
                          </div>
                        </div>
                      </div>
                    </div>
                  );
                })}
              </div>
            </div>

            {/* Quick Stats */}
            <div className="bg-white/[0.02] border border-white/[0.06] rounded-xl p-6">
              <div className="flex items-center gap-3 mb-6">
                <div className="w-8 h-8 bg-emerald-500/20 border border-emerald-500/30 rounded-lg flex items-center justify-center">
                  <BarChart3 className="w-4 h-4 text-emerald-400" />
                </div>
                <h2 className="text-xl font-medium text-white font-sans">Quick Stats</h2>
              </div>

              <div className="space-y-4">
                <div className="flex items-center justify-between p-3 bg-white/[0.02] border border-white/[0.04] rounded-lg">
                  <div className="flex items-center gap-2">
                    <Users className="w-4 h-4 text-white/60" />
                    <span className="text-sm text-white/70 font-sans">Active Agents</span>
                  </div>
                  <span className="text-sm font-medium text-white font-sans">2</span>
                </div>

                <div className="flex items-center justify-between p-3 bg-white/[0.02] border border-white/[0.04] rounded-lg">
                  <div className="flex items-center gap-2">
                    <TrendingUp className="w-4 h-4 text-white/60" />
                    <span className="text-sm text-white/70 font-sans">Total Trades</span>
                  </div>
                  <span className="text-sm font-medium text-white font-sans">38</span>
                </div>

                <div className="flex items-center justify-between p-3 bg-white/[0.02] border border-white/[0.04] rounded-lg">
                  <div className="flex items-center gap-2">
                    <DollarSign className="w-4 h-4 text-white/60" />
                    <span className="text-sm text-white/70 font-sans">Portfolio Value</span>
                  </div>
                  <span className="text-sm font-medium text-emerald-400 font-sans">$24,567</span>
                </div>

                <div className="flex items-center justify-between p-3 bg-white/[0.02] border border-white/[0.04] rounded-lg">
                  <div className="flex items-center gap-2">
                    <Award className="w-4 h-4 text-white/60" />
                    <span className="text-sm text-white/70 font-sans">Achievements</span>
                  </div>
                  <span className="text-sm font-medium text-white font-sans">7/12</span>
                </div>
              </div>
            </div>

            {/* Quick Actions */}
            <div className="bg-white/[0.02] border border-white/[0.06] rounded-xl p-6">
              <h2 className="text-lg font-medium text-white mb-4 font-sans">Quick Actions</h2>
              <div className="space-y-3">
                <button
                  onClick={() => navigate('/chat')}
                  className="w-full flex items-center gap-3 p-3 bg-white/[0.02] border border-white/[0.04] rounded-lg hover:border-emerald-500/30 hover:bg-emerald-500/5 transition-all duration-200 text-left"
                >
                  <MessageCircle className="w-4 h-4 text-emerald-400" />
                  <span className="text-sm text-white font-sans">Start Analysis</span>
                  <ChevronRight className="w-4 h-4 text-white/40 ml-auto" />
                </button>

                <button
                  onClick={() => navigate('/portfolio-builder')}
                  className="w-full flex items-center gap-3 p-3 bg-white/[0.02] border border-white/[0.04] rounded-lg hover:border-blue-500/30 hover:bg-blue-500/5 transition-all duration-200 text-left"
                >
                  <BarChart3 className="w-4 h-4 text-blue-400" />
                  <span className="text-sm text-white font-sans">Build Portfolio</span>
                  <ChevronRight className="w-4 h-4 text-white/40 ml-auto" />
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default Home;
