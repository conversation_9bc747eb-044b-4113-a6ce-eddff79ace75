import React from 'react';
import { useNavigate } from 'react-router-dom';
import {
  MessageCircle,
  TrendingUp,
  Briefcase,
  Hammer,
  Bot,
  Activity,
  ArrowRight
} from 'lucide-react';

const Home: React.FC = () => {
  const navigate = useNavigate();

  const mainFeatures = [
    {
      title: 'Chat Interface',
      description: 'AI-powered market analysis and trading insights',
      icon: <MessageCircle className="w-5 h-5" />,
      path: '/chat'
    },
    {
      title: 'Stock Scanner',
      description: 'Real-time market scanning and opportunity detection',
      icon: <TrendingUp className="w-5 h-5" />,
      path: '/agent-scanner'
    },
    {
      title: 'Portfolio Builder',
      description: 'Intelligent portfolio construction and management',
      icon: <Briefcase className="w-5 h-5" />,
      path: '/portfolio-builder'
    },
    {
      title: 'Agent Builder',
      description: 'Visual trading agent creation and deployment',
      icon: <Hammer className="w-5 h-5" />,
      path: '/agent-builder'
    }
  ];

  const tools = [
    {
      title: 'Trading Agents',
      description: 'Manage and monitor your AI trading agents',
      icon: <Bot className="w-4 h-4" />,
      path: '/agents'
    },
    {
      title: 'Backtesting',
      description: 'Test and validate your trading strategies',
      icon: <Activity className="w-4 h-4" />,
      path: '/agent-backtesting'
    }
  ];

  return (
    <div className="h-full bg-[#0A0A0A] text-white">
      <div className="max-w-5xl mx-auto px-8 py-16">

        {/* Header */}
        <div className="mb-20 text-center">
          <h1 className="text-4xl font-medium text-white mb-4 font-sans tracking-tight">
            Welcome to Osis.
          </h1>
          <p className="text-white/60 text-base max-w-lg mx-auto">
            Professional trading tools powered by AI
          </p>
        </div>

        {/* Main Features */}
        <div className="mb-20">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            {mainFeatures.map((feature, index) => (
              <div
                key={index}
                className="group relative backdrop-blur-xl bg-white/[0.02] border border-white/[0.08] rounded-2xl p-8 hover:bg-white/[0.04] transition-all duration-500 cursor-pointer shadow-[inset_0_1px_0_rgba(255,255,255,0.1),0_1px_3px_rgba(0,0,0,0.2)]"
                onClick={() => navigate(feature.path)}
              >
                <div className="flex items-start gap-5">
                  <div className="flex-shrink-0 w-12 h-12 rounded-xl bg-white/[0.05] border border-white/[0.1] flex items-center justify-center group-hover:bg-emerald-500/10 group-hover:border-emerald-500/20 transition-all duration-300">
                    <div className="text-white/80 group-hover:text-emerald-400 transition-colors duration-300">
                      {feature.icon}
                    </div>
                  </div>
                  <div className="flex-1 min-w-0">
                    <h3 className="font-medium text-white/90 group-hover:text-white transition-colors duration-300 mb-2 text-lg">
                      {feature.title}
                    </h3>
                    <p className="text-sm text-white/60 leading-relaxed">
                      {feature.description}
                    </p>
                  </div>
                  <ArrowRight className="w-4 h-4 text-white/30 group-hover:text-emerald-400 group-hover:translate-x-1 transition-all duration-300 mt-1" />
                </div>
              </div>
            ))}
          </div>
        </div>

        {/* Tools Section */}
        <div className="mb-16">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            {tools.map((tool, index) => (
              <div
                key={index}
                onClick={() => navigate(tool.path)}
                className="group relative backdrop-blur-xl bg-white/[0.015] border border-white/[0.06] rounded-xl p-6 hover:bg-white/[0.03] transition-all duration-300 cursor-pointer shadow-[inset_0_1px_0_rgba(255,255,255,0.05),0_1px_2px_rgba(0,0,0,0.1)]"
              >
                <div className="flex items-center gap-4">
                  <div className="flex-shrink-0 w-10 h-10 rounded-lg bg-white/[0.04] border border-white/[0.08] flex items-center justify-center group-hover:bg-white/[0.08] transition-all duration-300">
                    <div className="text-white/70 group-hover:text-white/90 transition-colors duration-300">
                      {tool.icon}
                    </div>
                  </div>
                  <div className="flex-1 min-w-0">
                    <h3 className="font-medium text-white/85 group-hover:text-white transition-colors duration-300 mb-1">
                      {tool.title}
                    </h3>
                    <p className="text-sm text-white/55 leading-relaxed">
                      {tool.description}
                    </p>
                  </div>
                  <ArrowRight className="w-4 h-4 text-white/25 group-hover:text-white/60 group-hover:translate-x-0.5 transition-all duration-300" />
                </div>
              </div>
            ))}
          </div>
        </div>

        {/* Get Started */}
        <div className="text-center">
          <button
            onClick={() => navigate('/chat')}
            className="inline-flex items-center gap-3 px-8 py-4 rounded-2xl backdrop-blur-xl bg-emerald-500/10 border border-emerald-500/20 hover:bg-emerald-500/15 hover:border-emerald-500/30 text-emerald-400 font-medium transition-all duration-300 shadow-[inset_0_1px_0_rgba(255,255,255,0.1),0_2px_8px_rgba(0,0,0,0.15)]"
          >
            <MessageCircle className="w-5 h-5" />
            Get Started
            <ArrowRight className="w-4 h-4" />
          </button>
        </div>

      </div>
    </div>
  );
};

export default Home;
