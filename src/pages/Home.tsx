import React from 'react';
import { useNavigate } from 'react-router-dom';
import {
  MessageCircle,
  TrendingUp,
  Briefcase,
  Hammer,
  Bot,
  Activity
} from 'lucide-react';

const Home: React.FC = () => {
  const navigate = useNavigate();

  const mainFeatures = [
    {
      title: 'Chat Interface',
      description: 'AI-powered market analysis and trading insights',
      icon: <MessageCircle className="w-5 h-5" />,
      path: '/chat',
      type: 'icon'
    },
    {
      title: 'Stock Scanner',
      description: 'Real-time market scanning and opportunity detection',
      icon: <TrendingUp className="w-5 h-5" />,
      path: '/agent-scanner',
      type: 'icon'
    },
    {
      title: 'Portfolio Builder',
      description: 'Intelligent portfolio construction and management',
      icon: <Briefcase className="w-5 h-5" />,
      path: '/portfolio-builder',
      type: 'icon'
    },
    {
      title: 'Agent Builder',
      description: 'Visual trading agent creation and deployment',
      icon: <Hammer className="w-5 h-5" />,
      path: '/agent-builder',
      type: 'image',
      imageUrl: 'https://pajqstbgncpbpcaffbpm.supabase.co/storage/v1/object/public/homeimages/build.png'
    }
  ];

  const tools = [
    {
      title: 'Trading Agents',
      description: 'Manage and monitor your AI trading agents',
      icon: <Bot className="w-4 h-4" />,
      path: '/agents'
    },
    {
      title: 'Backtesting',
      description: 'Test and validate your trading strategies',
      icon: <Activity className="w-4 h-4" />,
      path: '/agent-backtesting'
    }
  ];

  return (
    <div className="h-full text-white">
      <div className="max-w-5xl mx-auto px-8 py-16">

        {/* Header */}
        <div className="mb-20 text-left">
          <h1 className="text-6xl font-normal text-white mb-4 font-sans tracking-tight leading-tight">
            Welcome to Osis.
          </h1>
          <p className="text-2xl text-white/60 font-light tracking-wide">
            The playground for your imagination
          </p>
        </div>

        {/* Agent Builder Image */}
        <div className="mb-16 ml-[-1rem]">
          <div
            className="cursor-pointer"
            onClick={() => navigate('/agent-builder')}
          >
            <img
              src="https://pajqstbgncpbpcaffbpm.supabase.co/storage/v1/object/public/homeimages//aiagents.png"
              alt="Agent Builder"
              className="max-w-md h-auto object-cover rounded-lg"
            />
          </div>
        </div>

      </div>
    </div>
  );
};

export default Home;
