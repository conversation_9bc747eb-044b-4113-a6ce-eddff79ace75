import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import {
  MessageCircle,
  TrendingUp,
  Briefcase,
  Hammer,
  Bot,
  Activity,
  ArrowRight,
  BarChart3,
  Zap,
  Target,
  Command,
  Trophy,
  Star,
  CheckCircle,
  Clock,
  Users,
  DollarSign,
  Percent,
  Calendar,
  Award,
  Sparkles,
  ChevronRight,
  Play,
  Search,
  TestTube
} from 'lucide-react';
import { Button } from '@/components/ui/button';

const Home: React.FC = () => {
  const navigate = useNavigate();
  const [currentTime, setCurrentTime] = useState(new Date());

  useEffect(() => {
    const timer = setInterval(() => setCurrentTime(new Date()), 1000);
    return () => clearInterval(timer);
  }, []);

  // Mock data - in real app this would come from API
  const hasAgents = false; // Set to true when user has agents
  const hasCompletedActions = false; // Set to true when user has completed actions

  const accomplishments = hasCompletedActions ? [
    {
      id: 1,
      title: 'First Agent Created',
      completed: true,
      date: '2 days ago'
    },
    {
      id: 2,
      title: 'Portfolio Optimized',
      completed: true,
      date: '1 day ago'
    },
    {
      id: 3,
      title: 'Market Scanner Expert',
      completed: false,
      date: null
    }
  ] : [];

  const agentPerformance = hasAgents ? [
    {
      name: 'Growth Agent Alpha',
      performance: '+12.4%',
      trades: 23,
      status: 'active'
    },
    {
      name: 'Value Hunter Beta',
      performance: '+8.7%',
      trades: 15,
      status: 'testing'
    }
  ] : [];

  const nextActions = [
    {
      title: 'Create Your First Agent',
      description: 'Build an AI trading agent tailored to your strategy',
      icon: Hammer,
      action: () => navigate('/agent-builder')
    },
    {
      title: 'Run Market Scan',
      description: 'Discover opportunities across thousands of stocks',
      icon: Search,
      action: () => navigate('/agent-scanner')
    },
    {
      title: 'Test Strategy',
      description: 'Backtest your ideas with historical data',
      icon: TestTube,
      action: () => navigate('/agent-backtesting')
    }
  ];

  return (
    <div className="h-full bg-[#0A0A0A] text-white overflow-y-auto">
      {/* Navigator Buttons - Top Right */}
      <div className="absolute top-6 right-6 z-10 flex gap-3">
        <button
          onClick={() => navigate('/agent-builder')}
          className="flex items-center gap-2 bg-white hover:bg-white/95 text-black px-4 py-2 rounded-lg text-sm font-medium shadow-sm hover:shadow-md transition-all duration-200 font-sans"
        >
          <Hammer className="w-4 h-4" />
          <span>Build</span>
        </button>
        <button
          onClick={() => navigate('/agent-backtesting')}
          className="flex items-center gap-2 bg-white/[0.08] hover:bg-white/[0.12] text-white px-4 py-2 rounded-lg text-sm font-medium border border-white/[0.08] transition-all duration-200 font-sans"
        >
          <TestTube className="w-4 h-4" />
          <span>Test</span>
        </button>
        <button
          onClick={() => navigate('/agent-scanner')}
          className="flex items-center gap-2 bg-white/[0.08] hover:bg-white/[0.12] text-white px-4 py-2 rounded-lg text-sm font-medium border border-white/[0.08] transition-all duration-200 font-sans"
        >
          <Search className="w-4 h-4" />
          <span>Scan</span>
        </button>
      </div>

      <div className="max-w-6xl mx-auto px-8 py-12">
        {/* Header */}
        <div className="mb-16">
          <h1 className="text-4xl font-normal text-white mb-4 font-sans">
            Trading Headquarters
          </h1>
          <p className="text-white/60 text-lg font-sans">
            Welcome back • {currentTime.toLocaleDateString()} • {currentTime.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })}
          </p>
        </div>

        {/* Main Content */}
        <div className="space-y-16">
          {/* Accomplishments Section */}
          <div>
            <h2 className="text-2xl font-medium text-white mb-8 font-sans">Recent Activity</h2>
            {hasCompletedActions ? (
              <div className="space-y-4">
                {accomplishments.map((achievement) => (
                  <div key={achievement.id} className="flex items-center justify-between py-4 border-b border-white/[0.06]">
                    <div className="flex items-center gap-3">
                      {achievement.completed ? (
                        <CheckCircle className="w-5 h-5 text-white/60" />
                      ) : (
                        <Clock className="w-5 h-5 text-white/40" />
                      )}
                      <span className="text-white font-sans">{achievement.title}</span>
                    </div>
                    <span className="text-white/60 text-sm font-sans">{achievement.date || 'Pending'}</span>
                  </div>
                ))}
              </div>
            ) : (
              <div className="text-center py-12">
                <Trophy className="w-12 h-12 text-white/20 mx-auto mb-4" />
                <p className="text-white/60 font-sans">No activity yet. Start by creating your first agent.</p>
              </div>
            )}
          </div>

          {/* Agent Performance Section */}
          <div>
            <h2 className="text-2xl font-medium text-white mb-8 font-sans">Agent Performance</h2>
            {hasAgents ? (
              <div className="space-y-4">
                {agentPerformance.map((agent, index) => (
                  <div key={index} className="flex items-center justify-between py-4 border-b border-white/[0.06]">
                    <div className="flex items-center gap-3">
                      <div className={`w-3 h-3 rounded-full ${
                        agent.status === 'active' ? 'bg-white/60' : 'bg-white/30'
                      }`}></div>
                      <span className="text-white font-sans">{agent.name}</span>
                      <span className="text-white/60 text-sm font-sans">({agent.trades} trades)</span>
                    </div>
                    <span className="text-white font-sans">{agent.performance}</span>
                  </div>
                ))}
              </div>
            ) : (
              <div className="text-center py-12">
                <BarChart3 className="w-12 h-12 text-white/20 mx-auto mb-4" />
                <p className="text-white/60 font-sans">No agents created yet. Build your first trading agent to see performance data.</p>
              </div>
            )}
          </div>

          {/* Next Actions Section */}
          <div>
            <h2 className="text-2xl font-medium text-white mb-8 font-sans">Get Started</h2>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
              {nextActions.map((action, index) => {
                const Icon = action.icon;
                return (
                  <button
                    key={index}
                    onClick={action.action}
                    className="flex flex-col items-center text-center p-8 border border-white/[0.06] rounded-xl hover:border-white/[0.12] transition-all duration-200 group"
                  >
                    <Icon className="w-8 h-8 text-white/60 mb-4 group-hover:text-white transition-colors duration-200" />
                    <h3 className="text-lg font-medium text-white mb-2 font-sans">{action.title}</h3>
                    <p className="text-white/60 text-sm font-sans leading-relaxed">{action.description}</p>
                  </button>
                );
              })}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default Home;
