import React from 'react';
import { useNavigate } from 'react-router-dom';
import {
  MessageCircle,
  TrendingUp,
  Briefcase,
  Hammer,
  Bot,
  Activity
} from 'lucide-react';

const Home: React.FC = () => {
  const navigate = useNavigate();

  const mainFeatures = [
    {
      title: 'Chat Interface',
      description: 'AI-powered market analysis and trading insights',
      icon: <MessageCircle className="w-5 h-5" />,
      path: '/chat'
    },
    {
      title: 'Stock Scanner',
      description: 'Real-time market scanning and opportunity detection',
      icon: <TrendingUp className="w-5 h-5" />,
      path: '/agent-scanner'
    },
    {
      title: 'Portfolio Builder',
      description: 'Intelligent portfolio construction and management',
      icon: <Briefcase className="w-5 h-5" />,
      path: '/portfolio-builder'
    },
    {
      title: 'Agent Builder',
      description: 'Visual trading agent creation and deployment',
      icon: <Hammer className="w-5 h-5" />,
      path: '/agent-builder'
    }
  ];

  const tools = [
    {
      title: 'Trading Agents',
      description: 'Manage and monitor your AI trading agents',
      icon: <Bot className="w-4 h-4" />,
      path: '/agents'
    },
    {
      title: 'Backtesting',
      description: 'Test and validate your trading strategies',
      icon: <Activity className="w-4 h-4" />,
      path: '/agent-backtesting'
    }
  ];

  return (
    <div className="h-full bg-[#141414] text-white">
      <div className="max-w-5xl mx-auto px-8 py-16">

        {/* Header */}
        <div className="mb-20 text-left">
          <h1 className="text-6xl font-normal text-white mb-4 font-sans tracking-tight leading-tight">
            Welcome to Osis.
          </h1>
          <p className="text-2xl text-white/60 font-light tracking-wide">
            The playground for your imagination
          </p>
        </div>

        {/* Main Features */}
        <div className="mb-16">
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
            {mainFeatures.map((feature, index) => (
              <div
                key={index}
                className="group relative backdrop-blur-sm bg-white/[0.015] border border-white/[0.06] rounded-xl p-5 hover:bg-white/[0.03] hover:border-white/[0.12] transition-all duration-300 cursor-pointer"
                onClick={() => navigate(feature.path)}
              >
                <div className="text-center space-y-3">
                  <div className="w-8 h-8 mx-auto rounded-lg bg-white/[0.04] border border-white/[0.08] flex items-center justify-center group-hover:bg-emerald-500/10 group-hover:border-emerald-500/20 transition-all duration-300">
                    <div className="text-white/70 group-hover:text-emerald-400 transition-colors duration-300">
                      {React.cloneElement(feature.icon, { className: "w-4 h-4" })}
                    </div>
                  </div>
                  <div>
                    <h3 className="font-medium text-white/85 group-hover:text-white transition-colors duration-300 text-sm mb-1">
                      {feature.title}
                    </h3>
                    <p className="text-xs text-white/50 leading-relaxed">
                      {feature.description}
                    </p>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>

        {/* Tools Section */}
        <div className="mb-12">
          <div className="grid grid-cols-2 gap-3">
            {tools.map((tool, index) => (
              <div
                key={index}
                onClick={() => navigate(tool.path)}
                className="group relative backdrop-blur-sm bg-white/[0.01] border border-white/[0.04] rounded-lg p-4 hover:bg-white/[0.02] hover:border-white/[0.08] transition-all duration-300 cursor-pointer"
              >
                <div className="flex items-center gap-3">
                  <div className="flex-shrink-0 w-7 h-7 rounded-md bg-white/[0.03] border border-white/[0.06] flex items-center justify-center group-hover:bg-white/[0.06] transition-all duration-300">
                    <div className="text-white/60 group-hover:text-white/80 transition-colors duration-300">
                      {React.cloneElement(tool.icon, { className: "w-3 h-3" })}
                    </div>
                  </div>
                  <div className="flex-1 min-w-0">
                    <h3 className="font-medium text-white/80 group-hover:text-white transition-colors duration-300 text-xs mb-0.5">
                      {tool.title}
                    </h3>
                    <p className="text-xs text-white/45 leading-relaxed">
                      {tool.description}
                    </p>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>

        {/* Get Started */}
        <div className="text-left">
          <button
            onClick={() => navigate('/chat')}
            className="inline-flex items-center gap-2 px-6 py-3 rounded-lg backdrop-blur-sm bg-emerald-500/8 border border-emerald-500/15 hover:bg-emerald-500/12 hover:border-emerald-500/25 text-emerald-400 font-medium text-sm transition-all duration-300"
          >
            <MessageCircle className="w-4 h-4" />
            Get Started
          </button>
        </div>

      </div>
    </div>
  );
};

export default Home;
