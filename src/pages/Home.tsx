import React from 'react';
import { useNavigate } from 'react-router-dom';
import {
  MessageCircle,
  TrendingUp,
  Briefcase,
  Hammer,
  Bot,
  Activity,
  ArrowRight,
  BarChart3,
  Zap,
  Target,
  Command
} from 'lucide-react';
import { Button } from '@/components/ui/button';

const Home: React.FC = () => {
  const navigate = useNavigate();

  // Headquarters sections
  const headquarters = [
    {
      title: 'Command Center',
      description: 'AI-powered market analysis and real-time insights',
      icon: Command,
      path: '/chat',
      color: 'emerald'
    },
    {
      title: 'Market Scanner',
      description: 'Real-time opportunity detection across all markets',
      icon: Target,
      path: '/agent-scanner',
      color: 'blue'
    },
    {
      title: 'Portfolio Operations',
      description: 'Intelligent portfolio construction and management',
      icon: BarChart3,
      path: '/portfolio-builder',
      color: 'purple'
    },
    {
      title: 'Agent Testing Lab',
      description: 'Validate strategies with historical backtesting',
      icon: Activity,
      path: '/agent-backtesting',
      color: 'orange'
    }
  ];

  return (
    <div className="h-full text-white relative">
      {/* Build Your Agent Button - Top Right */}
      <div className="absolute top-8 right-8 z-10">
        <Button
          onClick={() => navigate('/agent-builder')}
          className="bg-gradient-to-r from-emerald-500 to-emerald-600 hover:from-emerald-600 hover:to-emerald-700 text-white px-6 py-3 rounded-xl text-sm font-medium shadow-[0_4px_20px_rgba(16,185,129,0.3)] hover:shadow-[0_6px_30px_rgba(16,185,129,0.4)] border-0 transition-all duration-300"
        >
          <div className="flex items-center gap-2">
            <Hammer className="w-4 h-4" />
            <span>Build Your Agent</span>
            <ArrowRight className="w-4 h-4" />
          </div>
        </Button>
      </div>

      <div className="max-w-7xl mx-auto px-8 py-16">
        {/* Headquarters Header */}
        <div className="mb-16 text-center">
          <h1 className="text-5xl font-normal text-white mb-6 font-sans tracking-tight leading-tight">
            Trading Headquarters
          </h1>
          <p className="text-xl text-white/60 font-light tracking-wide max-w-3xl mx-auto leading-relaxed">
            Your mission control center for AI-powered trading operations. Deploy agents, analyze markets, and execute strategies from your command center.
          </p>
        </div>

        {/* Headquarters Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-8 mb-16">
          {headquarters.map((section, index) => {
            const Icon = section.icon;
            const colorClasses = {
              emerald: 'from-emerald-500/20 to-emerald-600/10 border-emerald-500/30 hover:border-emerald-500/50',
              blue: 'from-blue-500/20 to-blue-600/10 border-blue-500/30 hover:border-blue-500/50',
              purple: 'from-purple-500/20 to-purple-600/10 border-purple-500/30 hover:border-purple-500/50',
              orange: 'from-orange-500/20 to-orange-600/10 border-orange-500/30 hover:border-orange-500/50'
            };

            const iconColors = {
              emerald: 'text-emerald-400',
              blue: 'text-blue-400',
              purple: 'text-purple-400',
              orange: 'text-orange-400'
            };

            return (
              <div
                key={index}
                onClick={() => navigate(section.path)}
                className={`group cursor-pointer p-8 rounded-2xl border transition-all duration-300 bg-gradient-to-br ${colorClasses[section.color]} hover:scale-[1.02] hover:shadow-[0_8px_40px_rgba(0,0,0,0.3)]`}
              >
                <div className="flex items-start gap-6">
                  <div className={`w-16 h-16 rounded-2xl bg-black/20 border border-white/10 flex items-center justify-center group-hover:scale-110 transition-transform duration-300`}>
                    <Icon className={`w-8 h-8 ${iconColors[section.color]}`} />
                  </div>
                  <div className="flex-1">
                    <h3 className="text-2xl font-semibold text-white mb-3 group-hover:text-white transition-colors">
                      {section.title}
                    </h3>
                    <p className="text-white/70 text-base leading-relaxed mb-4">
                      {section.description}
                    </p>
                    <div className="flex items-center gap-2 text-white/60 group-hover:text-white/80 transition-colors">
                      <span className="text-sm font-medium">Access {section.title}</span>
                      <ArrowRight className="w-4 h-4 group-hover:translate-x-1 transition-transform duration-300" />
                    </div>
                  </div>
                </div>
              </div>
            );
          })}
        </div>

        {/* Quick Actions */}
        <div className="bg-[#0D0D0D]/60 border border-white/[0.06] rounded-2xl p-8">
          <h2 className="text-2xl font-semibold text-white mb-6 text-center">Quick Deploy</h2>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            <button
              onClick={() => navigate('/chat')}
              className="p-6 bg-white/[0.02] border border-white/[0.06] rounded-xl hover:border-emerald-500/30 hover:bg-emerald-500/5 transition-all duration-300 text-left group"
            >
              <MessageCircle className="w-8 h-8 text-emerald-400 mb-4 group-hover:scale-110 transition-transform duration-300" />
              <h3 className="text-lg font-medium text-white mb-2">Start Analysis</h3>
              <p className="text-white/60 text-sm">Begin market analysis session</p>
            </button>

            <button
              onClick={() => navigate('/agent-scanner')}
              className="p-6 bg-white/[0.02] border border-white/[0.06] rounded-xl hover:border-blue-500/30 hover:bg-blue-500/5 transition-all duration-300 text-left group"
            >
              <Zap className="w-8 h-8 text-blue-400 mb-4 group-hover:scale-110 transition-transform duration-300" />
              <h3 className="text-lg font-medium text-white mb-2">Quick Scan</h3>
              <p className="text-white/60 text-sm">Run immediate market scan</p>
            </button>

            <button
              onClick={() => navigate('/agents')}
              className="p-6 bg-white/[0.02] border border-white/[0.06] rounded-xl hover:border-purple-500/30 hover:bg-purple-500/5 transition-all duration-300 text-left group"
            >
              <Bot className="w-8 h-8 text-purple-400 mb-4 group-hover:scale-110 transition-transform duration-300" />
              <h3 className="text-lg font-medium text-white mb-2">Deploy Agent</h3>
              <p className="text-white/60 text-sm">Activate trading agents</p>
            </button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default Home;
