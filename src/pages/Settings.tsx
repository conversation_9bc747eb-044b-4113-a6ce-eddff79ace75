import React, { useState, useEffect } from 'react';
import { Save, User, Mail, Lock, ChevronDown, Check, AlertCircle, Eye, EyeOff } from 'lucide-react';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
import { useToast } from '@/components/ui/use-toast';
import { supabase } from '@/integrations/supabase/client';

const Settings = () => {
  const { toast } = useToast();
  const [fullName, setFullName] = useState('');
  const [email, setEmail] = useState('');
  const [loading, setLoading] = useState(false);
  const [user, setUser] = useState<any>(null);
  const [activeSection, setActiveSection] = useState('profile');
  const [hasChanges, setHasChanges] = useState(false);
  const [originalFullName, setOriginalFullName] = useState('');

  // Password change states
  const [newPassword, setNewPassword] = useState('');
  const [confirmPassword, setConfirmPassword] = useState('');
  const [passwordError, setPasswordError] = useState('');
  const [showPasswordForm, setShowPasswordForm] = useState(false);
  const [showNewPassword, setShowNewPassword] = useState(false);
  const [showConfirmPassword, setShowConfirmPassword] = useState(false);
  const [passwordStrength, setPasswordStrength] = useState(0);

  useEffect(() => {
    const fetchUser = async () => {
      const { data: { user } } = await supabase.auth.getUser();
      if (user) {
        setUser(user);
        setEmail(user.email || '');

        // Fetch profile data if available
        const { data: profile } = await supabase
          .from('profiles')
          .select('full_name')
          .eq('id', user.id)
          .single();

        if (profile) {
          setFullName(profile.full_name || '');
          setOriginalFullName(profile.full_name || '');
        }
      }
    };

    fetchUser();
  }, []);

  // Track changes
  useEffect(() => {
    setHasChanges(fullName !== originalFullName);
  }, [fullName, originalFullName]);

  // Password strength calculator
  const calculatePasswordStrength = (password: string) => {
    let strength = 0;
    if (password.length >= 8) strength += 25;
    if (password.match(/[a-z]/)) strength += 25;
    if (password.match(/[A-Z]/)) strength += 25;
    if (password.match(/[0-9]/)) strength += 25;
    return strength;
  };

  useEffect(() => {
    setPasswordStrength(calculatePasswordStrength(newPassword));
  }, [newPassword]);

  const handleSaveChanges = async () => {
    setLoading(true);
    try {
      // Update profile in database
      const { error } = await supabase
        .from('profiles')
        .upsert({
          id: user.id,
          full_name: fullName,
          updated_at: new Date()
        });

      if (error) throw error;

      setOriginalFullName(fullName);
      setHasChanges(false);

      toast({
        title: "Profile Updated",
        description: "Your profile has been successfully updated.",
        duration: 3000,
      });
    } catch (error: any) {
      toast({
        title: "Error",
        description: error.message || "Failed to update profile. Please try again.",
        variant: "destructive",
        duration: 5000,
      });
    } finally {
      setLoading(false);
    }
  };

  const handlePasswordChange = async (e: React.FormEvent) => {
    e.preventDefault();
    setPasswordError('');

    if (newPassword !== confirmPassword) {
      setPasswordError("New passwords don't match");
      return;
    }

    if (newPassword.length < 8) {
      setPasswordError("Password must be at least 8 characters");
      return;
    }

    if (passwordStrength < 75) {
      setPasswordError('Password is too weak. Please include uppercase, lowercase, and numbers.');
      return;
    }

    setLoading(true);
    try {
      const { error } = await supabase.auth.updateUser({
        password: newPassword
      });

      if (error) throw error;

      // Reset form and show success
      setNewPassword('');
      setConfirmPassword('');
      setShowPasswordForm(false);

      toast({
        title: "Password Updated",
        description: "Your password has been successfully changed.",
        duration: 3000,
      });
    } catch (error: any) {
      setPasswordError(error.message || "Failed to change password");
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="min-h-screen bg-[#0A0A0A]">
      <div className="max-w-[600px] mx-auto px-4 sm:px-6 pt-16 pb-20">
        <div className="text-center mb-12">
          <h1 className="text-2xl sm:text-3xl font-semibold text-white mb-2" style={{ fontFamily: 'SF Pro Display, -apple-system, BlinkMacSystemFont, sans-serif' }}>Account Settings</h1>
          <p className="text-white/60 text-sm sm:text-base" style={{ fontFamily: 'SF Pro Text, -apple-system, BlinkMacSystemFont, sans-serif' }}>Manage your profile and security preferences</p>
        </div>

        <div className="bg-[#111111] rounded-2xl border border-white/[0.08] overflow-hidden">
          <div className="px-5 py-4 border-b border-white/[0.08]">
            <button
              onClick={() => setActiveSection(activeSection === 'profile' ? 'security' : 'profile')}
              className="w-full flex items-center justify-between text-left"
            >
              <div className="flex items-center gap-2">
                {activeSection === 'profile' ? (
                  <User className="h-4 w-4 text-white/60" />
                ) : (
                  <Lock className="h-4 w-4 text-white/60" />
                )}
                <h2 className="text-base font-medium text-white/90" style={{ fontFamily: 'SF Pro Display, -apple-system, BlinkMacSystemFont, sans-serif' }}>
                  {activeSection === 'profile' ? 'Profile Information' : 'Security'}
                </h2>
              </div>
              <ChevronDown className={`h-4 w-4 text-white/60 transition-transform duration-200 ${activeSection === 'security' ? 'rotate-180' : ''}`} />
            </button>
          </div>

          <div className="p-6">
            {activeSection === 'profile' && (
              <div className="space-y-6">
                <div className="space-y-2">
                  <label className="flex items-center gap-2 text-sm text-white/80" style={{ fontFamily: 'SF Pro Text, -apple-system, BlinkMacSystemFont, sans-serif' }}>
                    <User className="h-4 w-4 text-white/60" />
                    Full Name
                  </label>
                  <Input
                    type="text"
                    value={fullName}
                    onChange={(e) => setFullName(e.target.value)}
                    className="bg-[#0A0A0A] border-white/[0.08] h-11 rounded-lg text-white placeholder:text-white/40 focus:border-white/20 focus:ring-0 transition-colors duration-200"
                    style={{ fontFamily: 'SF Pro Text, -apple-system, BlinkMacSystemFont, sans-serif' }}
                    placeholder="Enter your full name"
                  />
                </div>

                <div className="space-y-2">
                  <label className="flex items-center gap-2 text-sm text-white/80" style={{ fontFamily: 'SF Pro Text, -apple-system, BlinkMacSystemFont, sans-serif' }}>
                    <Mail className="h-4 w-4 text-white/60" />
                    Email Address
                  </label>
                  <Input
                    type="email"
                    value={email}
                    disabled
                    className="bg-[#0A0A0A] border-white/[0.08] h-11 rounded-lg text-white/60 cursor-not-allowed"
                    style={{ fontFamily: 'SF Pro Text, -apple-system, BlinkMacSystemFont, sans-serif' }}
                  />
                  <p className="text-xs text-white/40" style={{ fontFamily: 'SF Pro Text, -apple-system, BlinkMacSystemFont, sans-serif' }}>
                    Email cannot be changed
                  </p>
                </div>

                <Button
                  onClick={handleSaveChanges}
                  disabled={loading || !hasChanges}
                  className={`w-full rounded-lg h-11 text-sm flex items-center justify-center gap-2 transition-all duration-200 font-medium ${
                    hasChanges
                      ? 'bg-emerald-600 hover:bg-emerald-700 text-white border-emerald-600'
                      : 'bg-white/[0.03] hover:bg-white/[0.06] text-white/60 border border-white/[0.08] cursor-not-allowed'
                  }`}
                  style={{ fontFamily: 'SF Pro Text, -apple-system, BlinkMacSystemFont, sans-serif' }}
                >
                  {loading ? (
                    <>
                      <div className="w-4 h-4 border-2 border-white/30 border-t-white rounded-full animate-spin" />
                      Saving...
                    </>
                  ) : hasChanges ? (
                    <>
                      <Save className="h-4 w-4" />
                      Save Changes
                    </>
                  ) : (
                    <>
                      <Check className="h-4 w-4" />
                      Saved
                    </>
                  )}
                </Button>
              </div>
            )}

            {activeSection === 'security' && (
              <div>
                {!showPasswordForm ? (
                  <>
                    <p className="text-white/60 text-sm mb-6" style={{ fontFamily: 'SF Pro Text, -apple-system, BlinkMacSystemFont, sans-serif' }}>
                      Manage your account security settings and password
                    </p>

                    <button
                      onClick={() => setShowPasswordForm(true)}
                      className="w-full rounded-lg h-11 text-sm bg-white/[0.03] hover:bg-white/[0.06] text-white/90 border border-white/[0.08] flex items-center justify-center gap-2 transition-colors duration-200"
                      style={{ fontFamily: 'SF Pro Text, -apple-system, BlinkMacSystemFont, sans-serif' }}
                    >
                      <Lock className="h-4 w-4" />
                      Change Password
                    </button>
                  </>
                ) : (
                  <form onSubmit={handlePasswordChange} className="space-y-5">
                    <div className="space-y-2">
                      <label className="text-sm text-white/80" style={{ fontFamily: 'SF Pro Text, -apple-system, BlinkMacSystemFont, sans-serif' }}>
                        New Password
                      </label>
                      <div className="relative">
                        <Input
                          type={showNewPassword ? "text" : "password"}
                          value={newPassword}
                          onChange={(e) => setNewPassword(e.target.value)}
                          className="bg-[#0A0A0A] border-white/[0.08] h-11 rounded-lg text-white placeholder:text-white/40 focus:border-white/20 focus:ring-0 transition-colors duration-200 pr-10"
                          style={{ fontFamily: 'SF Pro Text, -apple-system, BlinkMacSystemFont, sans-serif' }}
                          placeholder="Enter new password"
                          required
                        />
                        <button
                          type="button"
                          onClick={() => setShowNewPassword(!showNewPassword)}
                          className="absolute right-3 top-1/2 transform -translate-y-1/2 text-white/40 hover:text-white/60 transition-colors"
                        >
                          {showNewPassword ? <EyeOff className="h-4 w-4" /> : <Eye className="h-4 w-4" />}
                        </button>
                      </div>

                      {/* Password Strength Indicator */}
                      {newPassword && (
                        <div className="space-y-1">
                          <div className="flex justify-between text-xs">
                            <span className="text-white/60">Password Strength</span>
                            <span className={`font-medium ${
                              passwordStrength >= 100 ? 'text-emerald-400' :
                              passwordStrength >= 75 ? 'text-yellow-400' :
                              passwordStrength >= 50 ? 'text-orange-400' : 'text-red-400'
                            }`}>
                              {passwordStrength >= 100 ? 'Very Strong' :
                               passwordStrength >= 75 ? 'Strong' :
                               passwordStrength >= 50 ? 'Medium' : 'Weak'}
                            </span>
                          </div>
                          <div className="w-full bg-white/10 rounded-full h-1.5">
                            <div
                              className={`h-1.5 rounded-full transition-all duration-300 ${
                                passwordStrength >= 100 ? 'bg-emerald-500' :
                                passwordStrength >= 75 ? 'bg-yellow-500' :
                                passwordStrength >= 50 ? 'bg-orange-500' : 'bg-red-500'
                              }`}
                              style={{ width: `${passwordStrength}%` }}
                            />
                          </div>
                        </div>
                      )}
                    </div>

                    <div className="space-y-2">
                      <label className="text-sm text-white/80" style={{ fontFamily: 'SF Pro Text, -apple-system, BlinkMacSystemFont, sans-serif' }}>
                        Confirm New Password
                      </label>
                      <div className="relative">
                        <Input
                          type={showConfirmPassword ? "text" : "password"}
                          value={confirmPassword}
                          onChange={(e) => setConfirmPassword(e.target.value)}
                          className="bg-[#0A0A0A] border-white/[0.08] h-11 rounded-lg text-white placeholder:text-white/40 focus:border-white/20 focus:ring-0 transition-colors duration-200 pr-10"
                          style={{ fontFamily: 'SF Pro Text, -apple-system, BlinkMacSystemFont, sans-serif' }}
                          placeholder="Confirm new password"
                          required
                        />
                        <button
                          type="button"
                          onClick={() => setShowConfirmPassword(!showConfirmPassword)}
                          className="absolute right-3 top-1/2 transform -translate-y-1/2 text-white/40 hover:text-white/60 transition-colors"
                        >
                          {showConfirmPassword ? <EyeOff className="h-4 w-4" /> : <Eye className="h-4 w-4" />}
                        </button>
                      </div>
                    </div>

                    {passwordError && (
                      <p className="text-red-500/90 text-sm" style={{ fontFamily: 'SF Pro Text, -apple-system, BlinkMacSystemFont, sans-serif' }}>
                        {passwordError}
                      </p>
                    )}

                    <div className="flex gap-3 pt-1">
                      <Button
                        type="button"
                        onClick={() => {
                          setShowPasswordForm(false);
                          setPasswordError('');
                          setNewPassword('');
                          setConfirmPassword('');
                        }}
                        className="flex-1 rounded-lg h-11 text-sm bg-white/[0.03] hover:bg-white/[0.06] text-white/90 border border-white/[0.08] transition-colors duration-200"
                        style={{ fontFamily: 'SF Pro Text, -apple-system, BlinkMacSystemFont, sans-serif' }}
                      >
                        Cancel
                      </Button>

                      <Button
                        type="submit"
                        disabled={loading || passwordStrength < 75}
                        className={`flex-1 rounded-lg h-11 text-sm flex items-center justify-center gap-2 transition-all duration-200 font-medium ${
                          passwordStrength >= 75 && newPassword === confirmPassword && newPassword.length >= 8
                            ? 'bg-emerald-600 hover:bg-emerald-700 text-white border-emerald-600'
                            : 'bg-white/[0.03] hover:bg-white/[0.06] text-white/60 border border-white/[0.08] cursor-not-allowed'
                        }`}
                        style={{ fontFamily: 'SF Pro Text, -apple-system, BlinkMacSystemFont, sans-serif' }}
                      >
                        {loading ? (
                          <>
                            <div className="w-4 h-4 border-2 border-white/30 border-t-white rounded-full animate-spin" />
                            Updating...
                          </>
                        ) : (
                          <>
                            <Lock className="h-4 w-4" />
                            Update Password
                          </>
                        )}
                      </Button>
                    </div>
                  </form>
                )}
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  );
};

export default Settings;
