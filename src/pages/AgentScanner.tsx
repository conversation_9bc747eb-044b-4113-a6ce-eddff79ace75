import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Badge } from '@/components/ui/badge';
import { Loader2, Search, AlertCircle, ArrowUpRight, ArrowDownRight } from 'lucide-react';
import { useToast } from '@/components/ui/use-toast';
import { useAuth } from '@/contexts/AuthContext';
import { getAgentsByUserId } from '@/services/agentService';
import { supabase } from '@/integrations/supabase/client';

interface Agent {
  id?: string;
  name: string;
  description?: string;
  configuration: any;
}

interface ScanResult {
  symbol: string;
  signal: string;
  confidence: number;
  price: number;
  change: number;
  percentChange: number;
}

const AgentScanner: React.FC = () => {
  const { user } = useAuth();
  const { toast } = useToast();

  const [agents, setAgents] = useState<Agent[]>([]);
  const [selectedAgent, setSelectedAgent] = useState<string>('');
  const [selectedIndex, setSelectedIndex] = useState<string>('sp500');
  const [isScanning, setIsScanning] = useState(false);
  const [scanResults, setScanResults] = useState<ScanResult[]>([]);
  const [loadingAgents, setLoadingAgents] = useState(true);
  const [searchQuery, setSearchQuery] = useState('');

  // Market indices options
  const marketIndices = [
    { value: 'sp500', label: 'S&P 500' },
    { value: 'nasdaq', label: 'NASDAQ Composite' },
    { value: 'nasdaq100', label: 'NASDAQ 100' },
    { value: 'russell2000', label: 'Russell 2000' },
    { value: 'all', label: 'All Stocks' }
  ];

  // Load user's agents
  useEffect(() => {
    const loadAgents = async () => {
      if (!user?.id) return;

      try {
        const userAgents = await getAgentsByUserId(user.id);
        setAgents(userAgents);
      } catch (error) {
        console.error('Error loading agents:', error);
        toast({
          title: 'Error',
          description: 'Failed to load your agents',
          variant: 'destructive'
        });
      } finally {
        setLoadingAgents(false);
      }
    };

    loadAgents();
  }, [user?.id, toast]);

  // Run agent scanner
  const handleScan = async () => {
    if (!selectedAgent || !selectedIndex) {
      toast({
        title: 'Missing Selection',
        description: 'Please select both an agent and market index',
        variant: 'destructive'
      });
      return;
    }

    setIsScanning(true);
    setScanResults([]);

    try {
      // Call the agent scanner edge function
      const { data, error } = await supabase.functions.invoke('agent-scanner', {
        body: {
          agentId: selectedAgent,
          marketIndex: selectedIndex,
          userId: user?.id
        }
      });

      if (error) {
        throw error;
      }

      setScanResults(data.results || []);

      toast({
        title: 'Scan Complete',
        description: `Found ${data.results?.length || 0} bullish signals`,
      });
    } catch (error) {
      console.error('Error running agent scanner:', error);
      toast({
        title: 'Scan Failed',
        description: 'Failed to run agent scanner. Please try again.',
        variant: 'destructive'
      });
    } finally {
      setIsScanning(false);
    }
  };

  // Filter results based on search query
  const filteredResults = scanResults.filter(result =>
    result.symbol.toLowerCase().includes(searchQuery.toLowerCase())
  );

  if (loadingAgents) {
    return (
      <div className="h-full bg-[#0A0A0A] text-white flex items-center justify-center">
        <div className="flex flex-col items-center gap-4">
          <Loader2 className="h-8 w-8 animate-spin text-white/60" />
          <p className="text-white/60 text-sm">Loading your agents...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="h-full bg-[#0A0A0A] text-white flex flex-col">
      {/* Header Section */}
      <div className="px-8 py-6">
        <div className="flex items-start justify-between px-8 py-6 border-b border-white/[0.04]">
          {/* Header Text */}
          <div>
            <h1 className="text-4xl font-normal text-white mb-4 font-sans tracking-tight leading-tight">
              Agent Scanner
            </h1>
            <p className="text-white/50 text-sm">
              Run your trading agents against market data to find stocks that match your criteria
            </p>
          </div>

          {/* Scanner Configuration - Top Right */}
          <div className="flex items-end gap-4">
            <div className="space-y-1 w-40">
              <label className="text-xs font-medium text-white/60">Agent</label>
              <Select value={selectedAgent} onValueChange={setSelectedAgent}>
                <SelectTrigger className="backdrop-blur-sm bg-white/[0.02] border border-white/[0.08] text-white h-10 text-sm rounded-lg shadow-[inset_0_1px_0_rgba(255,255,255,0.1),0_1px_3px_rgba(0,0,0,0.2)] hover:bg-white/[0.04] transition-all duration-200">
                  <SelectValue placeholder="Select Agent" />
                </SelectTrigger>
                <SelectContent position="popper" side="bottom" align="start" className="bg-[#1A1A1A] border-white/[0.08] backdrop-blur-xl">
                  {agents.map((agent) => (
                    <SelectItem key={agent.id} value={agent.id} className="text-white hover:bg-white/[0.05] text-sm">
                      {agent.name}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
              {agents.length === 0 && (
                <p className="text-xs text-white/50 mt-1">
                  No agents found. Create an agent first.
                </p>
              )}
            </div>

            <div className="space-y-1 w-32">
              <label className="text-xs font-medium text-white/60">Market Scope</label>
              <Select value={selectedIndex} onValueChange={setSelectedIndex}>
                <SelectTrigger className="backdrop-blur-sm bg-white/[0.02] border border-white/[0.08] text-white h-10 text-sm rounded-lg shadow-[inset_0_1px_0_rgba(255,255,255,0.1),0_1px_3px_rgba(0,0,0,0.2)] hover:bg-white/[0.04] transition-all duration-200">
                  <SelectValue placeholder="Select Scope" />
                </SelectTrigger>
                <SelectContent position="popper" side="bottom" align="start" className="bg-[#1A1A1A] border-white/[0.08] backdrop-blur-xl">
                  {marketIndices.map((index) => (
                    <SelectItem key={index.value} value={index.value} className="text-white hover:bg-white/[0.05] text-sm">
                      {index.label}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            <div className="relative">
              <Button
                onClick={handleScan}
                disabled={!selectedAgent || !selectedIndex || isScanning}
                className={`relative overflow-hidden bg-white hover:bg-white/90 border border-white/20 hover:border-white/30 text-black hover:text-black shadow-[0_2px_8px_rgba(255,255,255,0.15),inset_0_1px_0_rgba(255,255,255,0.2)] h-10 text-sm font-medium rounded-lg transition-all duration-300 ${
                  isScanning ? 'px-6' : 'px-5'
                } disabled:opacity-50 disabled:cursor-not-allowed`}
              >
                {/* Animated border */}
                <div className="absolute inset-0 rounded-lg">
                  <div className="absolute inset-0 rounded-lg border-2 border-transparent bg-gradient-to-r from-white/0 via-white/40 to-white/0 animate-border-worm"></div>
                </div>

                <span className="relative z-20 flex items-center font-medium">
                  {isScanning ? (
                    <>
                      <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                      Scanning...
                    </>
                  ) : (
                    'Scan'
                  )}
                </span>
              </Button>
            </div>
          </div>
        </div>
      </div>

      {/* Scan Results */}
      {scanResults.length > 0 && (
        <div className="flex-1 px-8 pb-6">
          <div className="backdrop-blur-sm bg-[#0D0D0D]/80 border border-white/[0.06] rounded-xl h-full flex flex-col shadow-[0_8px_32px_rgba(0,0,0,0.4),inset_0_1px_0_rgba(255,255,255,0.08)]">
            <div className="p-6 pb-4 border-b border-white/[0.06]">
              <div className="flex items-center justify-between">
                <div>
                  <h2 className="text-lg font-medium text-white">Bullish Signals Detected</h2>
                  <p className="text-emerald-400 text-sm font-medium">{scanResults.length} opportunities found</p>
                </div>
                {scanResults.length > 5 && (
                  <div className="relative">
                    <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-white/40" />
                    <input
                      type="text"
                      placeholder="Search stocks..."
                      value={searchQuery}
                      onChange={(e) => setSearchQuery(e.target.value)}
                      className="pl-10 pr-4 py-2 bg-white/[0.02] border border-white/[0.08] rounded-lg text-white placeholder-white/40 focus:border-emerald-500/50 focus:outline-none text-sm w-56 transition-all duration-200"
                    />
                  </div>
                )}
              </div>
            </div>
            <div className="flex-1 overflow-y-auto p-6">
              <div className="space-y-3">
                {filteredResults.map((result, index) => {
                  const confidenceLevel = result.confidence >= 80 ? 'high' : result.confidence >= 60 ? 'medium' : 'low';

                  return (
                    <div
                      key={index}
                      className="backdrop-blur-sm bg-[#0A0A0A]/60 border border-white/[0.06] hover:bg-[#0A0A0A]/80 hover:border-white/[0.12] transition-all duration-300 rounded-xl p-5 shadow-[0_2px_8px_rgba(0,0,0,0.2)]"
                    >
                      <div className="flex items-center justify-between">
                        {/* Left side - Stock info */}
                        <div className="flex items-center gap-4">
                          <div className="flex flex-col">
                            <div className="flex items-center gap-3 mb-2">
                              <h3 className="font-bold text-white text-lg tracking-wide">{result.symbol}</h3>
                              <div className={`px-2 py-1 rounded-md text-xs font-medium border ${
                                confidenceLevel === 'high'
                                  ? 'bg-emerald-500/20 text-emerald-300 border-emerald-500/40'
                                  : confidenceLevel === 'medium'
                                  ? 'bg-yellow-500/20 text-yellow-300 border-yellow-500/40'
                                  : 'bg-orange-500/20 text-orange-300 border-orange-500/40'
                              }`}>
                                {result.confidence}% CONFIDENCE
                              </div>
                            </div>
                            <p className="text-emerald-400 text-sm font-medium uppercase tracking-wide">{result.signal}</p>
                          </div>
                        </div>

                        {/* Right side - Price and change */}
                        <div className="text-right">
                          <div className="text-xl font-bold text-white mb-1">
                            ${result.price.toFixed(2)}
                          </div>
                          <div className={`flex items-center justify-end gap-2 text-sm font-bold ${
                            result.change >= 0 ? 'text-emerald-400' : 'text-red-400'
                          }`}>
                            <div className={`w-6 h-6 rounded-full flex items-center justify-center ${
                              result.change >= 0 ? 'bg-emerald-500/20' : 'bg-red-500/20'
                            }`}>
                              {result.change >= 0 ? (
                                <ArrowUpRight className="h-3 w-3" />
                              ) : (
                                <ArrowDownRight className="h-3 w-3" />
                              )}
                            </div>
                            <div className="flex flex-col items-end">
                              <span className="text-sm">
                                {result.change >= 0 ? '+' : ''}${result.change.toFixed(2)}
                              </span>
                              <span className="text-xs opacity-80">
                                ({result.percentChange.toFixed(2)}%)
                              </span>
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                  );
                })}

                {filteredResults.length === 0 && searchQuery && (
                  <div className="text-center py-12">
                    <Search className="h-8 w-8 text-white/20 mx-auto mb-3" />
                    <h3 className="text-white/60 font-medium mb-1 text-sm">No stocks found</h3>
                    <p className="text-white/40 text-xs">
                      No stocks match your search for "{searchQuery}"
                    </p>
                  </div>
                )}
              </div>
            </div>
          </div>
        </div>
      )}

      {/* No Results Message */}
      {!isScanning && scanResults.length === 0 && selectedAgent && selectedIndex && (
        <div className="flex-1 flex items-center justify-center px-8 pb-6">
          <div className="bg-white/[0.02] border border-white/[0.06] rounded-lg max-w-md w-full p-8 text-center">
            <AlertCircle className="h-8 w-8 text-white/40 mx-auto mb-4" />
            <h3 className="text-sm font-medium mb-2 text-white">No Signals Found</h3>
            <p className="text-white/50 text-xs">
              Your agent didn't find any bullish signals in the selected market index.
              Try adjusting your agent's criteria or selecting a different market scope.
            </p>
          </div>
        </div>
      )}
    </div>
  );
};

export default AgentScanner;
