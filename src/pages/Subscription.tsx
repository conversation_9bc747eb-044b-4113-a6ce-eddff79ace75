import React, { useEffect, useState } from 'react';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { CheckCircle, ArrowLeft, Crown, Zap, TrendingUp, BarChart3, Bot, Sparkles, Star, ArrowRight, Check } from 'lucide-react';
import { But<PERSON> } from '@/components/ui/button';
import { Link, useNavigate } from 'react-router-dom';
import { SubscribeButton } from '@/components/subscription/SubscribeButton';
import { useSubscription } from '@/hooks/useSubscription';
import { useToast } from '@/components/ui/use-toast';

const Subscription = () => {
  const { toast } = useToast();
  const navigate = useNavigate();
  const { subscription, isLoadingSubscription, refetch } = useSubscription();
  const [selectedPlan, setSelectedPlan] = useState('pro');

  // Plan configurations
  const plans = [
    {
      id: 'basic',
      name: 'Basic',
      price: '$9',
      period: '/month',
      description: 'Perfect for getting started with AI trading',
      icon: Bot,
      features: [
        '5 AI Trading Agents',
        'Basic Backtesting',
        'Standard Market Scanner',
        'Email Support',
        'Basic Analytics'
      ],
      limitations: [
        'Limited to 100 scans/month',
        'Basic agent complexity',
        'Standard data feeds'
      ]
    },
    {
      id: 'pro',
      name: 'Professional',
      price: '$29',
      period: '/month',
      description: 'Advanced trading with premium features',
      icon: TrendingUp,
      popular: true,
      spotlight: true,
      features: [
        'Unlimited AI Trading Agents',
        'Advanced Backtesting & Analytics',
        'Real-time Market Scanner',
        'Priority Support',
        'Advanced Portfolio Management',
        'Custom Agent Builder',
        'Risk Management Tools',
        'API Access',
        'Advanced Chart Analysis'
      ],
      benefits: [
        'Up to 10x more profitable signals',
        'Real-time market opportunities',
        'Professional-grade analytics',
        'Custom strategy development'
      ]
    },
    {
      id: 'enterprise',
      name: 'Enterprise',
      price: '$99',
      period: '/month',
      description: 'Complete trading ecosystem for professionals',
      icon: Crown,
      features: [
        'Everything in Professional',
        'White-label Solutions',
        'Dedicated Account Manager',
        'Custom Integrations',
        'Advanced Risk Controls',
        'Institutional Data Feeds',
        'Multi-user Management',
        'Custom Reporting',
        'SLA Guarantee'
      ],
      benefits: [
        'Institutional-grade infrastructure',
        'Dedicated support team',
        'Custom feature development',
        'Enterprise security'
      ]
    }
  ];

  const currentPlan = plans.find(p => p.id === 'basic'); // Simulate current plan

  return (
    <div className="h-full bg-[#0A0A0A] text-white flex flex-col font-hanken-grotesk overflow-y-auto">
      {/* Header Section */}
      <div className="px-8 py-8 border-b border-white/[0.04]">
        <div className="max-w-6xl mx-auto">
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-4xl font-normal text-white mb-4 font-sans tracking-tight leading-tight">
                Upgrade Your Trading
              </h1>
              <p className="text-white/60 text-lg leading-relaxed">
                Unlock advanced AI trading capabilities and maximize your market potential
              </p>
            </div>
            <div className="flex items-center gap-3 text-sm">
              <div className="flex items-center gap-2 px-3 py-1.5 bg-white/[0.04] border border-white/[0.08] rounded-lg">
                <div className="w-2 h-2 bg-emerald-500 rounded-full"></div>
                <span className="text-white/70">Current: {currentPlan?.name}</span>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Plans Section */}
      <div className="flex-1 px-8 py-8">
        <div className="max-w-6xl mx-auto">
          <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
            {plans.map((plan) => {
              const Icon = plan.icon;
              const isSpotlight = plan.spotlight;
              const isCurrent = plan.id === 'basic';

              return (
                <div
                  key={plan.id}
                  className={`relative rounded-2xl border transition-all duration-300 ${
                    isSpotlight
                      ? 'bg-gradient-to-b from-emerald-500/10 to-emerald-600/5 border-emerald-500/30 shadow-[0_0_40px_rgba(16,185,129,0.15)] scale-105'
                      : 'bg-[#0D0D0D]/80 border-white/[0.06] hover:border-white/[0.12]'
                  }`}
                >
                  {/* Spotlight Badge */}
                  {isSpotlight && (
                    <div className="absolute -top-4 left-1/2 transform -translate-x-1/2">
                      <div className="flex items-center gap-2 px-4 py-2 bg-gradient-to-r from-emerald-500 to-emerald-600 rounded-full text-white text-sm font-medium shadow-lg">
                        <Star className="w-4 h-4" />
                        Most Popular
                      </div>
                    </div>
                  )}

                  {/* Current Plan Badge */}
                  {isCurrent && (
                    <div className="absolute -top-3 right-4">
                      <div className="flex items-center gap-1 px-3 py-1 bg-white/[0.08] border border-white/[0.12] rounded-full text-white/70 text-xs">
                        <Check className="w-3 h-3" />
                        Current Plan
                      </div>
                    </div>
                  )}

                  <div className="p-8">
                    {/* Plan Header */}
                    <div className="text-center mb-8">
                      <div className={`w-16 h-16 mx-auto mb-4 rounded-2xl flex items-center justify-center ${
                        isSpotlight
                          ? 'bg-gradient-to-br from-emerald-500/20 to-emerald-600/10 border border-emerald-500/30'
                          : 'bg-white/[0.04] border border-white/[0.08]'
                      }`}>
                        <Icon className={`w-8 h-8 ${isSpotlight ? 'text-emerald-400' : 'text-white/70'}`} />
                      </div>

                      <h3 className="text-2xl font-semibold text-white mb-2">{plan.name}</h3>
                      <p className="text-white/60 text-sm leading-relaxed mb-6">{plan.description}</p>

                      <div className="flex items-baseline justify-center gap-1">
                        <span className={`text-4xl font-bold ${isSpotlight ? 'text-emerald-400' : 'text-white'}`}>
                          {plan.price}
                        </span>
                        <span className="text-white/60 text-lg">{plan.period}</span>
                      </div>
                    </div>

                    {/* Features List */}
                    <div className="space-y-4 mb-8">
                      <h4 className="text-sm font-medium text-white/80 uppercase tracking-wide">Features Included</h4>
                      <div className="space-y-3">
                        {plan.features.map((feature, index) => (
                          <div key={index} className="flex items-start gap-3">
                            <Check className={`w-4 h-4 mt-0.5 flex-shrink-0 ${
                              isSpotlight ? 'text-emerald-400' : 'text-white/60'
                            }`} />
                            <span className="text-white/80 text-sm leading-relaxed">{feature}</span>
                          </div>
                        ))}
                      </div>
                    </div>

                    {/* Benefits (for spotlight plan) */}
                    {plan.benefits && (
                      <div className="space-y-4 mb-8 p-4 bg-emerald-500/5 border border-emerald-500/20 rounded-xl">
                        <h4 className="text-sm font-medium text-emerald-400 uppercase tracking-wide flex items-center gap-2">
                          <Sparkles className="w-4 h-4" />
                          Key Benefits
                        </h4>
                        <div className="space-y-2">
                          {plan.benefits.map((benefit, index) => (
                            <div key={index} className="flex items-start gap-3">
                              <TrendingUp className="w-4 h-4 mt-0.5 flex-shrink-0 text-emerald-400" />
                              <span className="text-emerald-100 text-sm leading-relaxed">{benefit}</span>
                            </div>
                          ))}
                        </div>
                      </div>
                    )}

                    {/* Action Button */}
                    <Button
                      className={`w-full h-12 text-sm font-medium rounded-xl transition-all duration-300 ${
                        isCurrent
                          ? 'bg-white/[0.04] border border-white/[0.08] text-white/60 cursor-not-allowed'
                          : isSpotlight
                          ? 'bg-gradient-to-r from-emerald-500 to-emerald-600 hover:from-emerald-600 hover:to-emerald-700 text-white shadow-[0_4px_20px_rgba(16,185,129,0.3)] hover:shadow-[0_6px_30px_rgba(16,185,129,0.4)] border-0'
                          : 'bg-white/[0.04] hover:bg-white/[0.08] border border-white/[0.08] hover:border-white/[0.15] text-white'
                      }`}
                      disabled={isCurrent}
                    >
                      {isCurrent ? (
                        'Current Plan'
                      ) : isSpotlight ? (
                        <div className="flex items-center gap-2">
                          <span>Upgrade to Professional</span>
                          <ArrowRight className="w-4 h-4" />
                        </div>
                      ) : (
                        `Upgrade to ${plan.name}`
                      )}
                    </Button>
                  </div>
                </div>
              );
            })}
          </div>

          {/* Additional Value Section */}
          <div className="mt-16 space-y-12">
            {/* Upgrade Benefits */}
            <div className="text-center">
              <h2 className="text-3xl font-semibold text-white mb-6">Why Upgrade to Professional?</h2>
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
                {[
                  {
                    icon: BarChart3,
                    title: 'Advanced Analytics',
                    description: 'Deep market insights with professional-grade charting and analysis tools'
                  },
                  {
                    icon: Zap,
                    title: 'Real-time Signals',
                    description: 'Get instant notifications for profitable trading opportunities as they happen'
                  },
                  {
                    icon: Bot,
                    title: 'Unlimited Agents',
                    description: 'Create and deploy unlimited AI trading agents with advanced customization'
                  },
                  {
                    icon: Crown,
                    title: 'Priority Support',
                    description: 'Get priority access to our expert support team and trading consultants'
                  }
                ].map((benefit, index) => {
                  const Icon = benefit.icon;
                  return (
                    <div key={index} className="p-6 bg-[#0D0D0D]/60 border border-white/[0.06] rounded-xl hover:border-emerald-500/30 transition-all duration-300">
                      <div className="w-12 h-12 mx-auto mb-4 rounded-xl bg-emerald-500/10 border border-emerald-500/20 flex items-center justify-center">
                        <Icon className="w-6 h-6 text-emerald-400" />
                      </div>
                      <h3 className="text-lg font-medium text-white mb-2">{benefit.title}</h3>
                      <p className="text-white/60 text-sm leading-relaxed">{benefit.description}</p>
                    </div>
                  );
                })}
              </div>
            </div>

            {/* Performance Stats */}
            <div className="bg-gradient-to-r from-emerald-500/10 to-emerald-600/5 border border-emerald-500/20 rounded-2xl p-8">
              <div className="text-center mb-8">
                <h3 className="text-2xl font-semibold text-white mb-3">Professional Users See Better Results</h3>
                <p className="text-emerald-100/80 text-lg">Real performance data from our Professional subscribers</p>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
                {[
                  { metric: '10.3x', label: 'More Profitable Signals', description: 'vs Basic plan users' },
                  { metric: '89%', label: 'Win Rate Average', description: 'Professional users' },
                  { metric: '24/7', label: 'Market Monitoring', description: 'Never miss opportunities' }
                ].map((stat, index) => (
                  <div key={index} className="text-center">
                    <div className="text-4xl font-bold text-emerald-400 mb-2">{stat.metric}</div>
                    <div className="text-lg font-medium text-white mb-1">{stat.label}</div>
                    <div className="text-emerald-100/60 text-sm">{stat.description}</div>
                  </div>
                ))}
              </div>
            </div>

            {/* Testimonials */}
            <div className="text-center">
              <h3 className="text-2xl font-semibold text-white mb-8">Trusted by Professional Traders</h3>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                {[
                  {
                    quote: "The Professional plan transformed my trading. The advanced analytics and real-time signals helped me increase my portfolio by 340% in just 6 months.",
                    author: "Sarah Chen",
                    role: "Quantitative Trader",
                    avatar: "SC"
                  },
                  {
                    quote: "Having unlimited AI agents means I can test multiple strategies simultaneously. The backtesting features are institutional-grade.",
                    author: "Marcus Rodriguez",
                    role: "Portfolio Manager",
                    avatar: "MR"
                  }
                ].map((testimonial, index) => (
                  <div key={index} className="p-6 bg-[#0D0D0D]/60 border border-white/[0.06] rounded-xl">
                    <div className="flex items-start gap-4">
                      <div className="w-12 h-12 bg-emerald-500/20 border border-emerald-500/30 rounded-full flex items-center justify-center text-emerald-400 font-medium text-sm">
                        {testimonial.avatar}
                      </div>
                      <div className="flex-1">
                        <p className="text-white/80 text-sm leading-relaxed mb-4 italic">"{testimonial.quote}"</p>
                        <div>
                          <div className="text-white font-medium text-sm">{testimonial.author}</div>
                          <div className="text-white/60 text-xs">{testimonial.role}</div>
                        </div>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </div>

            {/* Final CTA */}
            <div className="text-center bg-gradient-to-r from-emerald-500/5 to-emerald-600/5 border border-emerald-500/20 rounded-2xl p-8">
              <h3 className="text-2xl font-semibold text-white mb-4">Ready to Upgrade Your Trading?</h3>
              <p className="text-white/70 text-lg mb-6 max-w-2xl mx-auto">
                Join thousands of professional traders who've upgraded to unlock the full potential of AI-powered trading.
              </p>
              <Button className="bg-gradient-to-r from-emerald-500 to-emerald-600 hover:from-emerald-600 hover:to-emerald-700 text-white px-8 py-3 rounded-xl text-lg font-medium shadow-[0_4px_20px_rgba(16,185,129,0.3)] hover:shadow-[0_6px_30px_rgba(16,185,129,0.4)] border-0 transition-all duration-300">
                <div className="flex items-center gap-2">
                  <span>Upgrade to Professional Now</span>
                  <ArrowRight className="w-5 h-5" />
                </div>
              </Button>
              <p className="text-white/50 text-sm mt-4">30-day money-back guarantee • Cancel anytime</p>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default Subscription;
