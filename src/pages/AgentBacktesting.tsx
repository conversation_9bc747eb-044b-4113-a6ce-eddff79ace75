import React, { useState, useEffect } from 'react';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Badge } from '@/components/ui/badge';
import { Loader2, BarChart3, TrendingUp, TrendingDown, DollarSign } from 'lucide-react';
import PortfolioChart from '@/components/portfolio/PortfolioChart';
import { useToast } from '@/components/ui/use-toast';
import { useAuth } from '@/contexts/AuthContext';
import { getAgentsByUserId } from '@/services/agentService';
import { supabase } from '@/integrations/supabase/client';

interface Agent {
  id?: string; // Changed to optional based on agentService definition
  name: string;
  description?: string; // Changed to optional based on agentService definition
  configuration: any;
}

interface BacktestResult {
  symbol: string;
  startDate: string;
  endDate: string;
  totalReturn: number;
  buyAndHoldReturn: number;
  numberOfTrades: number;
  winRate: number;
  maxDrawdown: number;
  sharpeRatio: number;
  trades: Array<{
    date: string;
    type: 'buy' | 'sell';
    price: number;
    signal: string;
    confidence: number;
  }>;
  performanceChart: Array<{
    date: string;
    agentValue: number;
    buyHoldValue: number;
  }>;
}

const AgentBacktesting: React.FC = () => {
  const { user } = useAuth();
  const { toast } = useToast();

  const [agents, setAgents] = useState<Agent[]>([]);
  const [selectedAgent, setSelectedAgent] = useState<string>('');
  const [selectedSymbol, setSelectedSymbol] = useState<string>('AAPL');
  const [selectedTimeframe, setSelectedTimeframe] = useState<string>('1Y');
  const [selectedInterval, setSelectedInterval] = useState<string>('1D');
  const [isBacktesting, setIsBacktesting] = useState(false);
  const [backtestResult, setBacktestResult] = useState<BacktestResult | null>(null);
  const [loadingAgents, setLoadingAgents] = useState(true);

  // Get current date for reference
  const currentDate = new Date().toISOString().split('T')[0]; // YYYY-MM-DD format

  // Timeframe options (how much historical data to test)
  const timeframes = [
    { value: '3M', label: '3 Months' },
    { value: '6M', label: '6 Months' },
    { value: '1Y', label: '1 Year' },
    { value: '2Y', label: '2 Years' },
    { value: '5Y', label: '5 Years' }
  ];

  // Interval options (how often the agent runs)
  const intervals = [
    { value: '1D', label: 'Daily' },
    { value: '1W', label: 'Weekly' },
    { value: '1M', label: 'Monthly' }
  ];

  // Load user's agents
  useEffect(() => {
    const loadAgents = async () => {
      if (!user?.id) return;

      try {
        const userAgents: Agent[] = await getAgentsByUserId(user.id); // Explicitly cast to local Agent type
        setAgents(userAgents);
      } catch (error) {
        console.error('Error loading agents:', error);
        toast({
          title: 'Error',
          description: 'Failed to load your agents',
          variant: 'destructive'
        });
      } finally {
        setLoadingAgents(false);
      }
    };

    loadAgents();
  }, [user?.id, toast]);

  // Run backtest
  const handleBacktest = async () => {
    if (!selectedAgent || !selectedSymbol) {
      toast({
        title: 'Missing Information',
        description: 'Please select an agent and enter a stock symbol',
        variant: 'destructive'
      });
      return;
    }

    setIsBacktesting(true);
    setBacktestResult(null);

    try {
      // Call the agent backtesting edge function
      const { data, error } = await supabase.functions.invoke('agent-backtesting', {
        body: {
          agentId: selectedAgent,
          symbol: selectedSymbol.toUpperCase(),
          timeframe: selectedTimeframe,
          interval: selectedInterval,
          userId: user?.id,
          currentDate: currentDate // Pass current date for consistent calculations
        }
      });

      if (error) {
        throw error;
      }

      console.log('Backtest result:', data); // Debug log
      console.log('Performance chart data:', data.performanceChart); // Debug chart data

      // If performanceChart is missing, generate it from trade history
      if (!data.performanceChart || data.performanceChart.length === 0) {
        console.warn('No performance chart data returned, generating from trade history');

        if (data.trades && data.trades.length > 0) {
          const chartData = [];
          const baseValue = 10000; // Starting portfolio value

          // Filter and validate trades with proper dates
          const validTrades = data.trades.filter(trade => {
            const dateField = trade.date || trade.entryDate || trade.entry_date;
            if (!dateField) return false;

            const testDate = new Date(dateField);
            return !isNaN(testDate.getTime());
          });

          if (validTrades.length === 0) {
            console.warn('No valid trades with dates found');
            data.performanceChart = [];
            return;
          }

          // Sort trades by date
          const sortedTrades = [...validTrades].sort((a, b) => {
            const dateA = new Date(a.date || a.entryDate || a.entry_date);
            const dateB = new Date(b.date || b.entryDate || b.entry_date);
            return dateA.getTime() - dateB.getTime();
          });

          // Group trades by month and calculate monthly performance
          const monthlyData = new Map();
          let runningValue = baseValue;

          // Get date range
          const firstTradeDate = new Date(sortedTrades[0].date || sortedTrades[0].entryDate || sortedTrades[0].entry_date);

          // Add starting month
          const startMonth = new Date(firstTradeDate.getFullYear(), firstTradeDate.getMonth(), 1);
          const startKey = `${startMonth.getFullYear()}-${String(startMonth.getMonth() + 1).padStart(2, '0')}`;
          monthlyData.set(startKey, {
            date: startMonth.toISOString().split('T')[0],
            value: baseValue,
            trades: []
          });

          // Process trades and group by month
          for (const trade of sortedTrades) {
            const tradeDate = new Date(trade.date || trade.entryDate || trade.entry_date);
            const monthKey = `${tradeDate.getFullYear()}-${String(tradeDate.getMonth() + 1).padStart(2, '0')}`;

            if (!monthlyData.has(monthKey)) {
              const monthStart = new Date(tradeDate.getFullYear(), tradeDate.getMonth(), 1);
              monthlyData.set(monthKey, {
                date: monthStart.toISOString().split('T')[0],
                value: runningValue,
                trades: []
              });
            }

            monthlyData.get(monthKey).trades.push(trade);
          }

          // Calculate monthly performance
          const sortedMonths = Array.from(monthlyData.entries()).sort((a, b) => a[0].localeCompare(b[0]));

          for (let i = 0; i < sortedMonths.length; i++) {
            const [, monthData] = sortedMonths[i];

            // Calculate total return for this month
            let monthReturn = 0;
            for (const trade of monthData.trades) {
              const tradeReturn = trade.returnPercentage / 100;
              const positionSize = 0.1; // 10% position size
              monthReturn += tradeReturn * positionSize;
            }

            // Apply monthly return to running value
            if (i > 0) {
              runningValue = runningValue * (1 + monthReturn);
            }

            chartData.push({
              date: monthData.date,
              agentValue: runningValue,
              buyHoldValue: baseValue * (1 + 0.1 * (i / sortedMonths.length)) // 10% annual benchmark
            });
          }

          data.performanceChart = chartData;
        } else {
          console.warn('No trade history available to generate chart');
          data.performanceChart = [];
        }
      }

      setBacktestResult(data);

      toast({
        title: 'Backtest Complete',
        description: `Analyzed ${data.numberOfTrades} trades over ${selectedTimeframe}`,
      });
    } catch (error) {
      console.error('Error running backtest:', error);
      toast({
        title: 'Backtest Failed',
        description: 'Failed to run backtest. Please try again.',
        variant: 'destructive'
      });
    } finally {
      setIsBacktesting(false);
    }
  };

  if (loadingAgents) {
    return (
      <div className="h-full bg-[#0A0A0A] text-white flex items-center justify-center font-hanken-grotesk">
        <div className="flex flex-col items-center gap-4">
          <Loader2 className="h-8 w-8 animate-spin text-white/60" />
          <p className="mt-2 text-sm text-muted-foreground">Loading your agents...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="h-full bg-[#0A0A0A] text-white flex flex-col font-hanken-grotesk">
      {/* Header & Subtle Configuration */}
      <div className="px-8 py-6 border-b border-white/[0.04]">
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-4xl font-normal text-white mb-4 font-sans tracking-tight leading-tight">
              Agent Backtesting
            </h1>
            <p className="text-white/50 text-sm">
              Test your trading agents against historical data
            </p>
          </div>

          {/* Enhanced Configuration */}
          <div className="flex items-center gap-4">
            <Select value={selectedAgent} onValueChange={setSelectedAgent}>
              <SelectTrigger className="bg-white/[0.04] border border-white/[0.08] text-white h-10 text-sm w-40 rounded-md">
                <SelectValue placeholder="Select Agent" />
              </SelectTrigger>
              <SelectContent position="popper" side="bottom" align="start" className="bg-[#1A1A1A] border-white/[0.08]">
                {agents.map((agent) => (
                  <SelectItem key={agent.id} value={agent.id} className="text-white hover:bg-white/[0.05] text-sm">
                    {agent.name}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>

            <Input
              value={selectedSymbol}
              onChange={(e) => setSelectedSymbol(e.target.value.toUpperCase())}
              placeholder="AAPL"
              className="bg-white/[0.04] border border-white/[0.08] text-white h-10 text-sm uppercase placeholder-white/40 w-24 rounded-md"
            />

            <Select value={selectedTimeframe} onValueChange={setSelectedTimeframe}>
              <SelectTrigger className="bg-white/[0.04] border border-white/[0.08] text-white h-10 text-sm w-28 rounded-md">
                <SelectValue placeholder="Period" />
              </SelectTrigger>
              <SelectContent position="popper" side="bottom" align="start" className="bg-[#1A1A1A] border-white/[0.08]">
                {timeframes.map((timeframe) => (
                  <SelectItem key={timeframe.value} value={timeframe.value} className="text-white hover:bg-white/[0.05] text-sm">
                    {timeframe.label}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>

            <Button
              onClick={handleBacktest}
              disabled={!selectedAgent || !selectedSymbol || isBacktesting}
              className={`bg-white text-black hover:bg-gray-200 shadow-[0_2px_4px_rgba(0,0,0,0.2)] h-10 text-sm font-medium rounded-md transition-all duration-200 ${isBacktesting ? 'px-8' : 'px-6'}`}
            >
              <span className="flex items-center">
                {isBacktesting ? (
                  <>
                    <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                    Testing...
                  </>
                ) : (
                  'Test'
                )}
              </span>
            </Button>
          </div>
        </div>
      </div>

      {/* Backtest Results */}
      {backtestResult && (
        <div className="flex-1 overflow-y-auto">
          {/* Performance Chart - Above Cards */}
          {backtestResult.performanceChart && backtestResult.performanceChart.length > 0 ? (
            <div className="px-8 pt-6 pb-4">
              <div className="bg-[#0D0D0D] border border-white/[0.04] rounded-lg overflow-hidden">
                <PortfolioChart
                  data={backtestResult.performanceChart.map(point => ({
                    date: point.date,
                    value: point.agentValue
                  }))}
                  title="Agent Performance"
                  height={400}
                  loading={false}
                  showTrackingDot={true}
                />
              </div>
            </div>
          ) : (
            <div className="px-8 pt-6 pb-4">
              <div className="bg-[#0D0D0D] border border-white/[0.04] rounded-lg p-8 text-center">
                <p className="text-white/60 text-sm">Performance chart data not available</p>
                <p className="text-white/40 text-xs mt-2">The backtest completed but chart data was not generated</p>
              </div>
            </div>
          )}

          {/* Performance Metrics - Small Cards Below Chart */}
          <div className="px-8 pb-4">
            <div className="grid grid-cols-2 md:grid-cols-4 gap-3">
              <div className="bg-[#0D0D0D] border border-white/[0.04] rounded p-3 text-center">
                <div className={`text-lg font-medium ${
                  backtestResult.totalReturn > 0 ? 'text-emerald-400' : 'text-red-400'
                }`}>
                  {backtestResult.totalReturn > 0 ? '+' : ''}{backtestResult.totalReturn.toFixed(2)}%
                </div>
                <div className="text-xs text-white/60 mt-1">Agent Return</div>
              </div>

              <div className="bg-[#0D0D0D] border border-white/[0.04] rounded p-3 text-center">
                <div className="text-lg font-medium text-white">{backtestResult.numberOfTrades}</div>
                <div className="text-xs text-white/60 mt-1">Total Trades</div>
              </div>

              <div className="bg-[#0D0D0D] border border-white/[0.04] rounded p-3 text-center">
                <div className="text-lg font-medium text-white">
                  {backtestResult.winRate.toFixed(1)}%
                </div>
                <div className="text-xs text-white/60 mt-1">Win Rate</div>
              </div>

              <div className="bg-[#0D0D0D] border border-white/[0.04] rounded p-3 text-center">
                <div className="text-lg font-medium text-white">
                  -{backtestResult.maxDrawdown.toFixed(2)}%
                </div>
                <div className="text-xs text-white/60 mt-1">Max Drawdown</div>
              </div>
            </div>
          </div>

          {/* Trade History - Clean List */}
          <div className="px-8 pb-6">
            <div className="mb-3">
              <h3 className="text-sm font-medium text-white/80">Trade History</h3>
              <p className="text-xs text-white/50">Recent trades from your agent</p>
            </div>
            <div className="space-y-1 max-h-[300px] overflow-y-auto">
              {(backtestResult.trades ?? []).slice().reverse().map((trade, index) => (
                <div key={index} className="flex items-center justify-between py-2 px-3 hover:bg-white/[0.02] rounded transition-colors">
                  <div className="flex items-center gap-3">
                    <span className={`w-2 h-2 rounded-full ${
                      trade.type === 'buy' ? 'bg-emerald-400' : 'bg-red-400'
                    }`} />
                    <span className="text-xs text-white/60 font-mono w-20">{trade.date}</span>
                    <span className={`text-xs font-medium ${
                      trade.type === 'buy' ? 'text-emerald-400' : 'text-red-400'
                    }`}>
                      {trade.type.toUpperCase()}
                    </span>
                  </div>
                  <div className="flex items-center gap-3">
                    <span className="text-xs text-white/70">${trade.price.toFixed(2)}</span>
                    <span className="text-xs text-white/50">
                      {trade.signal} ({trade.confidence}%)
                    </span>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default AgentBacktesting;

